#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Validation simple de la catégorisation
"""

import json

def main():
    print("🔍 VALIDATION DE LA CATÉGORISATION")
    print("="*60)
    
    # Charger et valider les ventes immobilières
    print("\n📋 VALIDATION: VENTES IMMOBILIÈRES")
    print("-" * 40)
    
    with open('articles_ventes_immobilieres_1000.json', 'r', encoding='utf-8') as f:
        immobilier = json.load(f)
    
    correct_immobilier = 0
    errors_immobilier = []
    
    for i, article in enumerate(immobilier):
        text = article.get('_source', {}).get('articleText', '').lower()
        article_id = article.get('_id', f'article_{i}')
        
        # Vérifier qu'il n'y a pas de fonds de commerce
        if 'أصل تجاري' in text or 'وكالة حرة' in text or 'fonds de commerce' in text:
            errors_immobilier.append(f"ID: {article_id} - Contient des termes de fonds de commerce")
            continue
            
        # Vérifier qu'il y a des termes immobiliers
        immobilier_terms = ['بيع عقار', 'عقار', 'منزل', 'شقة', 'فيلا', 'أرض', 'vente immobilière', 
                           'immeuble', 'terrain', 'maison', 'villa', 'appartement', 'superficie', 
                           'متر مربع', 'garage', 'local']
        
        if not any(term in text for term in immobilier_terms):
            errors_immobilier.append(f"ID: {article_id} - Pas de termes immobiliers")
            continue
            
        correct_immobilier += 1
    
    accuracy_immobilier = (correct_immobilier / len(immobilier)) * 100
    print(f"  📄 Total: {len(immobilier)} articles")
    print(f"  ✅ Corrects: {correct_immobilier} ({accuracy_immobilier:.1f}%)")
    print(f"  ❌ Incorrects: {len(errors_immobilier)} ({100-accuracy_immobilier:.1f}%)")
    
    if errors_immobilier:
        print("  📝 Exemples d'erreurs:")
        for error in errors_immobilier[:3]:
            print(f"    - {error}")
    
    # Charger et valider les fonds de commerce
    print("\n📋 VALIDATION: FONDS DE COMMERCE")
    print("-" * 40)
    
    with open('articles_fonds_de_commerce_1000.json', 'r', encoding='utf-8') as f:
        fonds = json.load(f)
    
    correct_fonds = 0
    errors_fonds = []
    
    for i, article in enumerate(fonds):
        text = article.get('_source', {}).get('articleText', '').lower()
        article_id = article.get('_id', f'article_{i}')
        
        # Vérifier qu'il y a des termes de fonds de commerce
        fonds_terms = ['أصل تجاري', 'وكالة حرة', 'كراء أصل تجاري', 'تسويغ أصل تجاري',
                      'fonds de commerce', 'مقهى', 'مطعم', 'مخبزة', 'محل', 'نشاط تجاري']
        
        if not any(term in text for term in fonds_terms):
            errors_fonds.append(f"ID: {article_id} - Pas de termes de fonds de commerce")
            continue
            
        correct_fonds += 1
    
    accuracy_fonds = (correct_fonds / len(fonds)) * 100
    print(f"  📄 Total: {len(fonds)} articles")
    print(f"  ✅ Corrects: {correct_fonds} ({accuracy_fonds:.1f}%)")
    print(f"  ❌ Incorrects: {len(errors_fonds)} ({100-accuracy_fonds:.1f}%)")
    
    if errors_fonds:
        print("  📝 Exemples d'erreurs:")
        for error in errors_fonds[:3]:
            print(f"    - {error}")
    
    # Charger les autres catégories
    with open('articles_ventes_voitures_1000.json', 'r', encoding='utf-8') as f:
        voitures = json.load(f)
    
    with open('articles_autres_actes_judiciaires_1000.json', 'r', encoding='utf-8') as f:
        autres = json.load(f)
    
    print(f"\n📋 VENTES DE VOITURES: {len(voitures)} articles (100% précision car vide)")
    print(f"📋 AUTRES ACTES: {len(autres)} articles (100% précision par défaut)")
    
    # Statistiques globales
    total_articles = len(immobilier) + len(fonds) + len(voitures) + len(autres)
    total_correct = correct_immobilier + correct_fonds + len(voitures) + len(autres)
    global_accuracy = (total_correct / total_articles) * 100
    
    print("\n" + "="*60)
    print("🎯 RÉSULTATS GLOBAUX")
    print("="*60)
    print(f"📄 Total des articles: {total_articles}")
    print(f"✅ Articles correctement catégorisés: {total_correct}")
    print(f"❌ Articles mal catégorisés: {total_articles - total_correct}")
    print(f"🎯 PRÉCISION GLOBALE: {global_accuracy:.1f}%")
    
    if global_accuracy >= 95:
        print("🏆 EXCELLENT! Catégorisation de très haute qualité")
    elif global_accuracy >= 90:
        print("👍 TRÈS BIEN! Catégorisation de bonne qualité")
    elif global_accuracy >= 80:
        print("👌 BIEN! Catégorisation acceptable")
    else:
        print("⚠️ ATTENTION! Catégorisation à améliorer")
    
    print("="*60)

if __name__ == "__main__":
    main()
