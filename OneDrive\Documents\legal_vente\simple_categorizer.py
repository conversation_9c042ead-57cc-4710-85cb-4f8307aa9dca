#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simple pour regrouper les articles juridiques selon leur contenu
"""

import json
import re
from typing import Dict, List, Any

def extract_article_text(content: str) -> List[str]:
    """Extrait tous les textes d'articles du contenu"""
    # Pattern pour trouver les articleText
    pattern = r'"articleText":\s*"([^"]*)"'
    matches = re.findall(pattern, content, re.DOTALL)
    
    # Pattern pour les articleText avec guillemets triples
    pattern_triple = r'"articleText":\s*"""([^"]*?)"""'
    matches_triple = re.findall(pattern_triple, content, re.DOTALL)
    
    return matches + matches_triple

def categorize_text(text: str) -> str:
    """Catégorise un texte selon son contenu"""
    text_lower = text.lower()

    # Mots-clés d'exclusion pour les fonds de commerce (priorité absolue)
    fonds_commerce_exclusions = [
        'أصل تجاري', 'وكالة حرة', 'كراء أصل تجاري', 'تسويغ أصل تجاري',
        'بيع أصل تجاري', 'تجديد وكالة حرة', 'فسخ عقد كراء', 'إشهار بفسخ',
        'عناصر مادية ومعنوية', 'استغلال تجاري', 'نشاط تجاري',
        'fonds de commerce', 'fond commercial', 'location gérance', 'bail commercial'
    ]

    # Si c'est un fonds de commerce, on l'exclut de l'immobilier
    if any(keyword in text_lower for keyword in fonds_commerce_exclusions):
        return 'fonds_commerce'

    # Mots-clés pour les vraies ventes/locations immobilières
    immobilier_keywords = [
        'بيع عقار', 'بيع منزل', 'بيع شقة', 'بيع فيلا', 'بيع أرض', 'بيع دار',
        'مزاد عقاري', 'مزايدة عقارية', 'بيع بالمزاد العلني', 'سايسة عقارية',
        'عقلة عقارية', 'تبتيت عقار', 'رسم عقاري', 'ملكية عقارية',
        'كراء عقار', 'إيجار عقار', 'تأجير عقار',
        'متر مربع', 'مساحة', 'طابق أرضي', 'طابق علوي',
        'vente immobilière', 'saisie immobilière', 'enchères immobilières',
        'vente aux enchères publiques', 'adjudication', 'mise à prix',
        'titre foncier', 'propriété immobilière', 'terrain à bâtir'
    ]

    # Mots-clés pour les voitures (véhicules uniquement)
    voiture_keywords = [
        'سيارة', 'مركبة', 'عربة', 'سيارات', 'مركبات', 'عربات',
        'بيع سيارة', 'بيع مركبة', 'مزاد سيارات', 'مزايدة سيارات',
        'رقم التسجيل', 'لوحة ترقيم', 'محرك', 'موديل', 'ماركة',
        'قطع غيار السيارات', 'وكالة بيع السيارات', 'نافير', 'شالوتير',
        'voiture', 'véhicule', 'automobile', 'auto', 'car', 'navire', 'chalutier',
        'vente de voiture', 'vente véhicule', 'enchères voiture',
        'immatriculation', 'plaque d\'immatriculation', 'moteur', 'modèle', 'marque'
    ]

    # Mots-clés pour les fonds de commerce (activités commerciales)
    fonds_commerce_keywords = [
        'مقهى', 'مطعم', 'مخبزة', 'صيدلية', 'محل', 'دكان', 'حانوت', 'متجر',
        'مكتب', 'عيادة', 'ورشة', 'تجارة', 'زبائن', 'عملاء',
        'restaurant', 'café', 'boulangerie', 'pharmacie', 'magasin', 'boutique',
        'bureau', 'atelier', 'clientèle', 'achalandage', 'exploitation commerciale'
    ]

    # Compter les occurrences
    immobilier_count = sum(1 for keyword in immobilier_keywords if keyword in text_lower)
    voiture_count = sum(1 for keyword in voiture_keywords if keyword in text_lower)
    fonds_commerce_count = sum(1 for keyword in fonds_commerce_keywords if keyword in text_lower)

    # Logique de catégorisation
    if immobilier_count > 0 and immobilier_count >= max(voiture_count, fonds_commerce_count):
        return 'immobilier'
    elif voiture_count > 0 and voiture_count >= fonds_commerce_count:
        return 'voiture'
    elif fonds_commerce_count > 0:
        return 'fonds_commerce'
    else:
        return 'autres'

def extract_full_articles(content: str) -> List[Dict[str, Any]]:
    """Extrait les articles complets du contenu en préservant toutes les informations"""
    articles = []

    # Diviser le contenu en blocs d'articles
    lines = content.split('\n')
    current_article_lines = []
    brace_count = 0
    in_article = False

    for line in lines:
        stripped = line.strip()
        if not stripped:
            continue

        # Détecter le début d'un article
        if stripped.startswith('{') and not in_article:
            in_article = True
            current_article_lines = [line]
            brace_count = line.count('{') - line.count('}')
        elif in_article:
            current_article_lines.append(line)
            brace_count += line.count('{') - line.count('}')

            # Fin de l'article
            if brace_count <= 0:
                article_content = '\n'.join(current_article_lines)

                # Méthode 1: Essayer de parser directement après nettoyage des guillemets triples
                try:
                    # Nettoyer les guillemets triples dans articleText
                    def clean_article_text(match):
                        text = match.group(1)
                        text = text.replace(chr(10), " ").replace(chr(13), " ")
                        text = text.replace("\\", "\\\\").replace('"', '\\"')
                        return f'"articleText": "{text}"'

                    cleaned_content = re.sub(
                        r'"articleText":\s*"""([^"]*?)"""',
                        clean_article_text,
                        article_content,
                        flags=re.DOTALL
                    )

                    article = json.loads(cleaned_content)
                    articles.append(article)

                except Exception as e1:
                    # Méthode 2: Extraction manuelle des champs principaux
                    try:
                        article = {}

                        # Extraire _index
                        index_match = re.search(r'"_index":\s*"([^"]*)"', article_content)
                        if index_match:
                            article['_index'] = index_match.group(1)

                        # Extraire _id
                        id_match = re.search(r'"_id":\s*"([^"]*)"', article_content)
                        if id_match:
                            article['_id'] = id_match.group(1)

                        # Extraire _score
                        score_match = re.search(r'"_score":\s*([0-9.]+)', article_content)
                        if score_match:
                            article['_score'] = float(score_match.group(1))

                        # Extraire _source
                        source_match = re.search(r'"_source":\s*{(.*)}(?:\s*})?$', article_content, re.DOTALL | re.MULTILINE)
                        if source_match:
                            source_content = source_match.group(1)

                            # Construire l'objet _source
                            source = {}

                            # doc_id
                            doc_id_match = re.search(r'"doc_id":\s*"([^"]*)"', source_content)
                            if doc_id_match:
                                source['doc_id'] = doc_id_match.group(1)

                            # title
                            title_match = re.search(r'"title":\s*"([^"]*)"', source_content)
                            if title_match:
                                source['title'] = title_match.group(1)

                            # reference
                            ref_match = re.search(r'"reference":\s*"([^"]*)"', source_content)
                            if ref_match:
                                source['reference'] = ref_match.group(1)

                            # lang
                            lang_match = re.search(r'"lang":\s*"([^"]*)"', source_content)
                            if lang_match:
                                source['lang'] = lang_match.group(1)

                            # articleText (le plus important)
                            article_text_match = re.search(r'"articleText":\s*(?:"""([^"]*?)"""|"([^"]*)")', source_content, re.DOTALL)
                            if article_text_match:
                                source['articleText'] = article_text_match.group(1) or article_text_match.group(2)

                            # publishedAt
                            published_match = re.search(r'"publishedAt":\s*"([^"]*)"', source_content)
                            if published_match:
                                source['publishedAt'] = published_match.group(1)

                            # file
                            file_match = re.search(r'"file":\s*"([^"]*)"', source_content)
                            if file_match:
                                source['file'] = file_match.group(1)

                            # source
                            source_name_match = re.search(r'"source":\s*"([^"]*)"', source_content)
                            if source_name_match:
                                source['source'] = source_name_match.group(1)

                            # source_grps
                            source_grps_match = re.search(r'"source_grps":\s*\[(.*?)\]', source_content, re.DOTALL)
                            if source_grps_match:
                                grps_content = source_grps_match.group(1)
                                grps = re.findall(r'"([^"]*)"', grps_content)
                                source['source_grps'] = grps

                            # categories (structure complexe)
                            categories_match = re.search(r'"categories":\s*\[(.*?)\]', source_content, re.DOTALL)
                            if categories_match:
                                categories_content = categories_match.group(1)
                                categories = []

                                # Extraire chaque catégorie
                                category_blocks = re.findall(r'\{([^}]*)\}', categories_content)
                                for block in category_blocks:
                                    category = {}

                                    # slug
                                    slug_match = re.search(r'"slug":\s*"([^"]*)"', block)
                                    if slug_match:
                                        category['slug'] = slug_match.group(1)

                                    # names
                                    names_match = re.search(r'"names":\s*\{([^}]*)\}', block)
                                    if names_match:
                                        names_content = names_match.group(1)
                                        names = {}

                                        ar_match = re.search(r'"ar":\s*"([^"]*)"', names_content)
                                        if ar_match:
                                            names['ar'] = ar_match.group(1)

                                        fr_match = re.search(r'"fr":\s*"([^"]*)"', names_content)
                                        if fr_match:
                                            names['fr'] = fr_match.group(1)

                                        category['names'] = names

                                    if category:
                                        categories.append(category)

                                source['categories'] = categories
                            else:
                                source['categories'] = []

                            # page
                            page_match = re.search(r'"page":\s*(null|\d+)', source_content)
                            if page_match:
                                page_val = page_match.group(1)
                                source['page'] = None if page_val == 'null' else int(page_val)

                            # extras
                            source['extras'] = None

                            article['_source'] = source

                        if article:  # Si on a réussi à extraire quelque chose
                            articles.append(article)

                    except Exception as e2:
                        print(f"⚠️ Article ignoré (erreur d'extraction): {str(e2)[:100]}")

                current_article_lines = []
                in_article = False

    return articles

def main():
    """Fonction principale"""
    print("🔍 Début de la catégorisation des articles juridiques...")
    
    # Lire le fichier
    try:
        with open('articles.json', 'r', encoding='utf-8') as f:
            content = f.read()
        print("✅ Fichier lu avec succès")
    except Exception as e:
        print(f"❌ Erreur lors de la lecture: {e}")
        return
    
    # Extraire les articles
    print("📋 Extraction des articles...")
    articles = extract_full_articles(content)
    print(f"✅ {len(articles)} articles extraits")
    
    # Catégoriser
    categories = {
        'immobilier': [],
        'voiture': [],
        'fonds_commerce': [],
        'autres': []
    }
    
    for article in articles:
        try:
            article_text = article.get('_source', {}).get('articleText', '')
            if article_text:
                category = categorize_text(article_text)
                categories[category].append(article)
            else:
                categories['autres'].append(article)
        except:
            categories['autres'].append(article)
    
    # Statistiques
    total = len(articles)
    print("\n" + "="*50)
    print("📊 STATISTIQUES DE CATÉGORISATION")
    print("="*50)
    print(f"📄 Total des articles: {total}")
    print(f"🏠 Ventes immobilières: {len(categories['immobilier'])} ({len(categories['immobilier'])/total*100:.1f}%)")
    print(f"🚗 Ventes de voitures: {len(categories['voiture'])} ({len(categories['voiture'])/total*100:.1f}%)")
    print(f"🏪 Fonds de commerce: {len(categories['fonds_commerce'])} ({len(categories['fonds_commerce'])/total*100:.1f}%)")
    print(f"⚖️ Autres actes judiciaires: {len(categories['autres'])} ({len(categories['autres'])/total*100:.1f}%)")
    print("="*50)
    
    # Sauvegarder
    category_names = {
        'immobilier': 'ventes_immobilieres',
        'voiture': 'ventes_voitures',
        'fonds_commerce': 'fonds_de_commerce',
        'autres': 'autres_actes_judiciaires'
    }
    
    for category, articles_list in categories.items():
        filename = f"articles_{category_names[category]}.json"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(articles_list, f, ensure_ascii=False, indent=2)
            print(f"✅ Sauvegardé {len(articles_list)} articles dans {filename}")
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde de {filename}: {e}")
    
    print("✅ Catégorisation terminée avec succès!")

if __name__ == "__main__":
    main()
