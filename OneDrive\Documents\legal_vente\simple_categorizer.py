#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simple pour regrouper les articles juridiques selon leur contenu
"""

import json
import re
from typing import Dict, List, Any

def extract_article_text(content: str) -> List[str]:
    """Extrait tous les textes d'articles du contenu"""
    # Pattern pour trouver les articleText
    pattern = r'"articleText":\s*"([^"]*)"'
    matches = re.findall(pattern, content, re.DOTALL)
    
    # Pattern pour les articleText avec guillemets triples
    pattern_triple = r'"articleText":\s*"""([^"]*?)"""'
    matches_triple = re.findall(pattern_triple, content, re.DOTALL)
    
    return matches + matches_triple

def categorize_text(text: str) -> str:
    """Catégorise un texte selon son contenu"""
    text_lower = text.lower()
    
    # Mots-clés pour l'immobilier
    immobilier_keywords = [
        'بيع عقار', 'بيع منزل', 'عقار', 'منزل', 'دار', 'شقة', 'فيلا', 'أرض',
        'محل سكني', 'محل سكنى', 'بناية', 'عمارة', 'مساحة', 'متر مربع', 'طابق',
        'غرفة', 'صالة', 'مطبخ', 'حمام', 'شرفة', 'حديقة', 'فناء',
        'رسم عقاري', 'ملكية عقارية', 'عقلة عقارية', 'سكني',
        'immobilier', 'immeuble', 'maison', 'villa', 'appartement', 'terrain',
        'propriété', 'bâtiment', 'résidence', 'logement', 'superficie',
        'chambre', 'salon', 'cuisine', 'salle', 'garage', 'jardin', 'terrasse',
        'titre foncier', 'parcelle', 'vente immobilière', 'saisie immobilière'
    ]
    
    # Mots-clés pour les voitures
    voiture_keywords = [
        'سيارة', 'مركبة', 'عربة', 'سيارات', 'مركبات', 'عربات',
        'بيع سيارة', 'بيع مركبة', 'مزاد سيارات',
        'voiture', 'véhicule', 'automobile', 'auto', 'car',
        'vente de voiture', 'vente véhicule', 'enchères voiture'
    ]
    
    # Compter les occurrences
    immobilier_count = sum(1 for keyword in immobilier_keywords if keyword in text_lower)
    voiture_count = sum(1 for keyword in voiture_keywords if keyword in text_lower)
    
    if immobilier_count > 0 and immobilier_count >= voiture_count:
        return 'immobilier'
    elif voiture_count > 0:
        return 'voiture'
    else:
        return 'autres'

def extract_full_articles(content: str) -> List[Dict[str, Any]]:
    """Extrait les articles complets du contenu"""
    articles = []
    
    # Diviser le contenu en blocs d'articles
    # Chercher les patterns qui commencent par {
    lines = content.split('\n')
    current_article_lines = []
    brace_count = 0
    in_article = False
    
    for line in lines:
        stripped = line.strip()
        if not stripped:
            continue
            
        # Détecter le début d'un article
        if stripped.startswith('{') and not in_article:
            in_article = True
            current_article_lines = [line]
            brace_count = line.count('{') - line.count('}')
        elif in_article:
            current_article_lines.append(line)
            brace_count += line.count('{') - line.count('}')
            
            # Fin de l'article
            if brace_count <= 0:
                try:
                    article_text = '\n'.join(current_article_lines)
                    # Nettoyer les guillemets triples
                    article_text = re.sub(r'"""([^"]*?)"""', r'"\1"', article_text, flags=re.DOTALL)
                    
                    # Essayer de parser
                    article = json.loads(article_text)
                    articles.append(article)
                except Exception as e:
                    # Extraire au moins le texte de l'article
                    article_content = '\n'.join(current_article_lines)
                    article_text_match = re.search(r'"articleText":\s*(?:"""([^"]*?)"""|"([^"]*)")', article_content, re.DOTALL)
                    if article_text_match:
                        text = article_text_match.group(1) or article_text_match.group(2)
                        # Créer un article minimal
                        articles.append({
                            '_source': {
                                'articleText': text,
                                'title': 'Article extrait',
                                'reference': f'extracted_{len(articles)}'
                            }
                        })
                
                current_article_lines = []
                in_article = False
    
    return articles

def main():
    """Fonction principale"""
    print("🔍 Début de la catégorisation des articles juridiques...")
    
    # Lire le fichier
    try:
        with open('articles.json', 'r', encoding='utf-8') as f:
            content = f.read()
        print("✅ Fichier lu avec succès")
    except Exception as e:
        print(f"❌ Erreur lors de la lecture: {e}")
        return
    
    # Extraire les articles
    print("📋 Extraction des articles...")
    articles = extract_full_articles(content)
    print(f"✅ {len(articles)} articles extraits")
    
    # Catégoriser
    categories = {
        'immobilier': [],
        'voiture': [],
        'autres': []
    }
    
    for article in articles:
        try:
            article_text = article.get('_source', {}).get('articleText', '')
            if article_text:
                category = categorize_text(article_text)
                categories[category].append(article)
            else:
                categories['autres'].append(article)
        except:
            categories['autres'].append(article)
    
    # Statistiques
    total = len(articles)
    print("\n" + "="*50)
    print("📊 STATISTIQUES DE CATÉGORISATION")
    print("="*50)
    print(f"📄 Total des articles: {total}")
    print(f"🏠 Ventes immobilières: {len(categories['immobilier'])} ({len(categories['immobilier'])/total*100:.1f}%)")
    print(f"🚗 Ventes de voitures: {len(categories['voiture'])} ({len(categories['voiture'])/total*100:.1f}%)")
    print(f"⚖️ Autres actes judiciaires: {len(categories['autres'])} ({len(categories['autres'])/total*100:.1f}%)")
    print("="*50)
    
    # Sauvegarder
    category_names = {
        'immobilier': 'ventes_immobilieres',
        'voiture': 'ventes_voitures', 
        'autres': 'autres_actes_judiciaires'
    }
    
    for category, articles_list in categories.items():
        filename = f"articles_{category_names[category]}.json"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(articles_list, f, ensure_ascii=False, indent=2)
            print(f"✅ Sauvegardé {len(articles_list)} articles dans {filename}")
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde de {filename}: {e}")
    
    print("✅ Catégorisation terminée avec succès!")

if __name__ == "__main__":
    main()
