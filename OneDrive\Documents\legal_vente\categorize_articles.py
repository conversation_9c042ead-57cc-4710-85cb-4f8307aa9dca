#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour regrouper les articles juridiques selon leur contenu :
1. Ventes immobilières 
2. Ventes de voitures
3. Autres actes judiciaires
"""

import json
import re
from typing import Dict, List, Any

def load_articles(file_path: str) -> List[Dict[str, Any]]:
    """Charge les articles depuis le fichier JSON"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les guillemets triples par des guillemets simples échappés
        content = content.replace('"""', '"')

        # Essayer de charger le JSON
        articles = json.loads(content)
        print(f"✅ Chargé {len(articles)} articles depuis {file_path}")
        return articles
    except json.JSONDecodeError as e:
        print(f"❌ Erreur JSON: {e}")
        print("🔧 Tentative de réparation du fichier...")
        return load_articles_with_repair(file_path)
    except Exception as e:
        print(f"❌ Erreur lors du chargement du fichier: {e}")
        return []

def load_articles_with_repair(file_path: str) -> List[Dict[str, Any]]:
    """Charge les articles en réparant le format JSON"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        articles = []
        current_article = ""
        brace_count = 0
        in_article = False

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Détecter le début d'un article
            if line.startswith('{') and not in_article:
                in_article = True
                current_article = line
                brace_count = line.count('{') - line.count('}')
            elif in_article:
                current_article += " " + line
                brace_count += line.count('{') - line.count('}')

                # Fin de l'article
                if brace_count == 0:
                    try:
                        # Nettoyer le texte
                        cleaned = current_article.replace('"""', '"')
                        # Essayer de parser l'article
                        article = json.loads(cleaned)
                        articles.append(article)
                    except:
                        # Si ça échoue, essayer une approche plus agressive
                        try:
                            # Remplacer les caractères problématiques
                            cleaned = re.sub(r'"""[^"]*"""', '""', current_article)
                            article = json.loads(cleaned)
                            articles.append(article)
                        except:
                            print(f"⚠️ Article ignoré (format invalide)")

                    current_article = ""
                    in_article = False

        print(f"✅ Réparé et chargé {len(articles)} articles")
        return articles

    except Exception as e:
        print(f"❌ Erreur lors de la réparation: {e}")
        return []

def categorize_article(article_text: str) -> str:
    """
    Catégorise un article selon son contenu
    Retourne: 'immobilier', 'voiture', ou 'autres'
    """
    text_lower = article_text.lower()
    
    # Mots-clés pour l'immobilier (arabe et français)
    immobilier_keywords = [
        # Arabe
        'بيع عقار', 'بيع منزل', 'عقار', 'منزل', 'دار', 'شقة', 'فيلا', 'أرض', 'قطعة أرض',
        'محل سكني', 'محل سكنى', 'بناية', 'عمارة', 'مساحة', 'متر مربع', 'طابق',
        'غرفة', 'صالة', 'مطبخ', 'حمام', 'مرحاض', 'شرفة', 'حديقة', 'فناء',
        'رسم عقاري', 'تسجيل عقاري', 'ملكية عقارية', 'عقلة عقارية', 'سكني',
        'تجاري', 'فلاحي', 'صناعي', 'سياحي', 'إقامة', 'مجمع سكني',
        # Français
        'immobilier', 'immeuble', 'maison', 'villa', 'appartement', 'terrain',
        'propriété', 'bâtiment', 'construction', 'résidence', 'logement',
        'superficie', 'chambre', 'salon', 'cuisine', 'salle', 'garage',
        'jardin', 'terrasse', 'balcon', 'étage', 'rez-de-chaussée',
        'titre foncier', 'cadastre', 'parcelle', 'lotissement',
        'vente immobilière', 'saisie immobilière', 'enchères publiques'
    ]
    
    # Mots-clés pour les voitures (arabe et français)
    voiture_keywords = [
        # Arabe
        'سيارة', 'مركبة', 'عربة', 'سيارات', 'مركبات', 'عربات',
        'بيع سيارة', 'بيع مركبة', 'مزاد سيارات', 'مزايدة سيارات',
        'رقم التسجيل', 'لوحة ترقيم', 'محرك', 'موديل', 'ماركة',
        # Français
        'voiture', 'véhicule', 'automobile', 'auto', 'car',
        'voitures', 'véhicules', 'automobiles', 'autos',
        'vente de voiture', 'vente véhicule', 'enchères voiture',
        'immatriculation', 'plaque', 'moteur', 'modèle', 'marque',
        'garage automobile', 'concessionnaire'
    ]
    
    # Compter les occurrences de mots-clés
    immobilier_count = sum(1 for keyword in immobilier_keywords if keyword in text_lower)
    voiture_count = sum(1 for keyword in voiture_keywords if keyword in text_lower)
    
    # Logique de catégorisation
    if immobilier_count > 0 and immobilier_count >= voiture_count:
        return 'immobilier'
    elif voiture_count > 0:
        return 'voiture'
    else:
        return 'autres'

def categorize_articles(articles: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Catégorise tous les articles
    Retourne un dictionnaire avec les trois catégories
    """
    categories = {
        'immobilier': [],
        'voiture': [],
        'autres': []
    }
    
    for article in articles:
        try:
            article_text = article.get('_source', {}).get('articleText', '')
            if article_text:
                category = categorize_article(article_text)
                categories[category].append(article)
            else:
                categories['autres'].append(article)
        except Exception as e:
            print(f"⚠️ Erreur lors de la catégorisation d'un article: {e}")
            categories['autres'].append(article)
    
    return categories

def save_categorized_articles(categories: Dict[str, List[Dict[str, Any]]], base_path: str):
    """Sauvegarde les articles catégorisés dans des fichiers séparés"""
    
    category_names = {
        'immobilier': 'ventes_immobilieres',
        'voiture': 'ventes_voitures', 
        'autres': 'autres_actes_judiciaires'
    }
    
    for category, articles in categories.items():
        filename = f"{base_path}_{category_names[category]}.json"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)
            print(f"✅ Sauvegardé {len(articles)} articles dans {filename}")
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde de {filename}: {e}")

def print_statistics(categories: Dict[str, List[Dict[str, Any]]]):
    """Affiche les statistiques de catégorisation"""
    total = sum(len(articles) for articles in categories.values())
    
    print("\n" + "="*50)
    print("📊 STATISTIQUES DE CATÉGORISATION")
    print("="*50)
    print(f"📄 Total des articles: {total}")
    print(f"🏠 Ventes immobilières: {len(categories['immobilier'])} ({len(categories['immobilier'])/total*100:.1f}%)")
    print(f"🚗 Ventes de voitures: {len(categories['voiture'])} ({len(categories['voiture'])/total*100:.1f}%)")
    print(f"⚖️ Autres actes judiciaires: {len(categories['autres'])} ({len(categories['autres'])/total*100:.1f}%)")
    print("="*50)

def main():
    """Fonction principale"""
    print("🔍 Début de la catégorisation des articles juridiques...")
    
    # Charger les articles
    articles = load_articles('articles.json')
    if not articles:
        return
    
    # Catégoriser les articles
    print("📋 Catégorisation en cours...")
    categories = categorize_articles(articles)
    
    # Afficher les statistiques
    print_statistics(categories)
    
    # Sauvegarder les résultats
    print("💾 Sauvegarde des fichiers catégorisés...")
    save_categorized_articles(categories, 'articles')
    
    print("✅ Catégorisation terminée avec succès!")

if __name__ == "__main__":
    main()
