#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script STRICT pour extraire les détails - Met NULL si pas trouvé
Évite les fausses détections
"""

import json
import re
from typing import Optional

def extract_prix_strict(text: str) -> Optional[str]:
    """Extrait le prix de manière STRICTE - évite les fausses détections"""
    if not text:
        return None
    
    # D'abord, exclure les contextes qui ne sont PAS des prix
    exclusions_contexte = [
        'وصل عدد',  # Numéro de reçu
        'تسجيل عدد',  # Numéro d'enregistrement
        'بطاقة التعريف الوطنية عدد',  # Numéro de carte d'identité
        'الهاتف',  # Numéro de téléphone
        'الفاكس',  # Numéro de fax
        'المعرف الجبائي',  # Identifiant fiscal
        'رقم',  # Numéro
        'عدد الهاتف',  # Numéro de téléphone
        'phone', 'tel', 'fax'
    ]
    
    # Vérifier si le texte contient des exclusions
    text_lower = text.lower()
    for exclusion in exclusions_contexte:
        if exclusion in text_lower:
            # Si c'est un contexte d'exclusion, être plus strict
            pass
    
    # Patterns STRICTS mais COMPLETS pour les prix avec contexte obligatoire
    prix_patterns_stricts = [
        # Prix avec contexte arabe EXPLICITE de vente/prix (PATTERN MANQUÉ AJOUTÉ)
        r'(?:الثمن الإفتتاحي|الثمن الافتتاحي)\s*[:\s]*([0-9.,\s]+)د',  # NOUVEAU: "الثمن الإفتتاحي : 192.850,864د"
        r'(?:بثمن افتتاحي قدره|الثمن الافتتاحي|بالأمن الإفتتاحي|ثمن البيع|سعر البيع)\s*[:\s]*([0-9.,\s]+)',
        r'(?:بثمن افتتاحي قدره|الثمن الافتتاحي|بالأمن الإفتتاحي)\s*[^0-9]*\(([0-9.,\s]+)\)',

        # Prix avec contexte français EXPLICITE
        r'(?:mise à prix|prix de vente|prix d\'adjudication|montant de la vente)\s*[:\s]*([0-9.,\s]+)',
        r'(?:vendu pour|adjugé pour|cédé pour)\s*[:\s]*([0-9.,\s]+)',

        # Prix avec dinars EXPLICITES
        r'([0-9.,\s]+)\s*(?:dinars?|دينار|د)\s*(?:tunisiens?)?',  # AMÉLIORÉ: ajouté "د"

        # Prix entre parenthèses avec dinar EXPLICITE
        r'\(([0-9.,\s]+)\s*(?:د|دينار|dinars?)\)',

        # Prix avec mots arabes + parenthèses (contexte de vente)
        r'(?:مائة|ألف|آلاف|مليون)[^(]*(?:دينار)[^(]*\(([0-9.,\s]+)\)',
    ]
    
    for pattern in prix_patterns_stricts:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            prix = match if isinstance(match, str) else match
            prix_clean = re.sub(r'[^\d.,]', '', prix)
            
            # Validation stricte
            if prix_clean and len(prix_clean) >= 3:
                # Éviter les numéros de téléphone, ID, etc.
                if not re.match(r'^[0-9]{8,10}$', prix_clean):  # Pas de numéros de 8-10 chiffres
                    # Vérifier que ce n'est pas dans un contexte d'exclusion
                    if not is_in_exclusion_context(text, prix):
                        return prix_clean
    
    return None

def is_in_exclusion_context(text: str, prix: str) -> bool:
    """Vérifie si le prix est dans un contexte d'exclusion"""
    # Chercher le prix dans le texte et vérifier le contexte autour
    prix_escaped = re.escape(prix)
    
    # Patterns d'exclusion avec contexte
    exclusion_patterns = [
        f'وصل عدد[^0-9]*{prix_escaped}',
        f'تسجيل عدد[^0-9]*{prix_escaped}',
        f'بطاقة التعريف[^0-9]*{prix_escaped}',
        f'الهاتف[^0-9]*{prix_escaped}',
        f'المعرف الجبائي[^0-9]*{prix_escaped}',
        f'phone[^0-9]*{prix_escaped}',
        f'tel[^0-9]*{prix_escaped}',
    ]
    
    for pattern in exclusion_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            return True
    
    return False

def extract_ville_strict(text: str) -> Optional[str]:
    """Extrait la ville de manière STRICTE"""
    if not text:
        return None
    
    # Villes tunisiennes connues
    villes_tunisiennes = [
        'tunis', 'تونس', 'sfax', 'صفاقس', 'sousse', 'سوسة', 'kairouan', 'القيروان',
        'bizerte', 'بنزرت', 'gabès', 'قابس', 'ariana', 'أريانة', 'gafsa', 'قفصة',
        'monastir', 'المنستير', 'ben arous', 'بن عروس', 'kasserine', 'القصرين',
        'medenine', 'مدنين', 'nabeul', 'نابل', 'tataouine', 'تطاوين',
        'beja', 'باجة', 'jendouba', 'جندوبة', 'mahdia', 'المهدية',
        'manouba', 'منوبة', 'siliana', 'سليانة', 'tozeur', 'توزر',
        'zaghouan', 'زغوان', 'kef', 'الكاف', 'sidi bouzid', 'سيدي بوزيد',
        'kebili', 'قبلي', 'la marsa', 'المرسى', 'carthage', 'قرطاج',
        'بني حسان', 'جمال', 'أريانة', 'اريانة'
    ]
    
    text_lower = text.lower()
    
    # Chercher les villes directement
    for ville in villes_tunisiennes:
        if ville.lower() in text_lower:
            return ville.title()
    
    # Patterns stricts pour extraire les lieux
    lieu_patterns_stricts = [
        r'(?:القاطن|القاطنة|قاطن|قاطنة)\s+(?:ب|في)\s*([^،.\n]{3,30})',
        r'(?:كائن|الكائن)\s+(?:ب|في)\s*([^،.\n]{3,30})',
        r'(?:بالمحكمة الابتدائية|المحكمة الابتدائية)\s+(?:ب|في)?\s*([^،.\n]{3,30})',
        r'(?:tribunal|cour)\s+(?:de|d\'|du)\s*([^,.\n]{3,30})',
        r'(?:sis|situé)\s+(?:à|au|en)\s+([^,.\n]{3,30})',
    ]
    
    for pattern in lieu_patterns_stricts:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            lieu = match.strip()
            lieu = re.sub(r'[^\w\s\u0600-\u06FF]', '', lieu)
            if 3 <= len(lieu) <= 30:
                return lieu.title()
    
    return None

def extract_avocat_strict(text: str) -> Optional[str]:
    """Extrait l'avocat de manière STRICTE"""
    if not text:
        return None
    
    # Patterns stricts pour les avocats
    avocat_patterns_stricts = [
        r'نائبه\s+(?:الاستاذ|الأستاذ)\s+([^،.\n]{3,50})\s+(?:المحامي)',
        r'نائبته\s+(?:الاستاذة|الأستاذة)\s+([^،.\n]{3,50})\s+(?:المحامية)',
        r'(?:بمكتب محاميتهم|مكتب محاميتهم)\s+(?:الأستاذة|الأستاذ)\s+([^،.\n]{3,50})',
        r'(?:الأستاذة|الأستاذ)\s+([^،.\n]{3,50})\s+(?:المحامية|المحامي)\s+(?:لدى|ب)',
        r'(?:maître|me)\s+([^,.\n]{3,50})\s+(?:avocat|avocate)',
        r'(?:avocat|avocate)\s*:\s*(?:maître|me)?\s*([^,.\n]{3,50})',
    ]
    
    for pattern in avocat_patterns_stricts:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            avocat = match.strip()
            avocat = clean_avocat_name_strict(avocat)
            if avocat:
                return avocat
    
    return None

def clean_avocat_name_strict(avocat: str) -> Optional[str]:
    """Nettoie le nom de l'avocat de manière stricte"""
    if not avocat:
        return None
    
    # Normaliser les espaces
    avocat = re.sub(r'\s+', ' ', avocat)
    
    # Enlever les caractères spéciaux
    avocat = re.sub(r'[*\-_\(\)\[\]{}]+', '', avocat)
    
    # Enlever les suffixes
    avocat = re.sub(r'\s+(?:لدى|المحامية|المحامي|avocat|avocate).*', '', avocat)
    
    # Validation stricte
    avocat = avocat.strip()
    if 3 <= len(avocat) <= 50 and not re.match(r'^\d+$', avocat):
        # Éviter les mots génériques
        mots_generiques = ['الكائن', 'بعدد', 'شارع', 'نهج', 'الهاتف']
        if not any(mot in avocat.lower() for mot in mots_generiques):
            return avocat.title()
    
    return None

def process_all_categories_strict():
    """Traite TOUTES les catégories avec logique stricte - REMPLACE les fichiers existants"""
    print("🔒 EXTRACTION STRICTE UNIVERSELLE - NULL SI PAS TROUVÉ")
    print("="*70)
    print("⚠️  ATTENTION: Remplace TOUS les fichiers existants avec logique stricte")
    print("="*70)

    categories = [
        ('articles_ventes_immobilieres_1000.json', 'Immobilier'),
        ('articles_fonds_de_commerce_1000.json', 'Fonds de Commerce'),
        ('articles_ventes_voitures_1000.json', 'Voitures'),
        ('articles_autres_actes_judiciaires_1000.json', 'Autres Actes'),
        ('articles_immobiliers_ultra_complet.json', 'Immobilier Ultra-Complet')
    ]
    
    for filename, category_name in categories:
        print(f"\n📋 TRAITEMENT: {category_name}")
        print("-" * 40)
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                articles = json.load(f)
            print(f"✅ Chargé {len(articles)} articles")
        except Exception as e:
            print(f"❌ Erreur: {e}")
            continue
        
        if not articles:
            print("⚠️ Aucun article à traiter")
            continue
        
        # Statistiques
        stats = {'prix': 0, 'ville': 0, 'avocat': 0, 'total': len(articles)}
        
        for article in articles:
            source = article.get('_source', {})
            article_text = source.get('articleText', '')
            
            # Extraire avec logique stricte
            prix = extract_prix_strict(article_text)
            ville = extract_ville_strict(article_text)
            avocat = extract_avocat_strict(article_text)
            
            # Mettre à jour les champs (NULL si pas trouvé)
            source['prix'] = prix
            source['ville'] = ville
            source['avocat'] = avocat
            
            # Compter les extractions réussies
            if prix: stats['prix'] += 1
            if ville: stats['ville'] += 1
            if avocat: stats['avocat'] += 1
        
        # Afficher les statistiques
        total = stats['total']
        print(f"💰 Prix extraits: {stats['prix']}/{total} ({stats['prix']/total*100:.1f}%)")
        print(f"🏙️ Villes extraites: {stats['ville']}/{total} ({stats['ville']/total*100:.1f}%)")
        print(f"⚖️ Avocats extraits: {stats['avocat']}/{total} ({stats['avocat']/total*100:.1f}%)")
        
        # Sauvegarder - REMPLACE le fichier original avec version stricte
        if 'ultra_complet' in filename:
            # Pour le fichier ultra-complet, créer une nouvelle version stricte
            output_filename = 'articles_immobiliers_final_strict.json'
        else:
            # Pour les autres, remplacer directement
            output_filename = filename

        try:
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)
            print(f"✅ Sauvegardé (REMPLACÉ): {output_filename}")
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")

    print("\n" + "="*70)
    print("🎉 Extraction stricte universelle terminée!")
    print("📁 TOUS les fichiers ont été mis à jour avec la logique stricte")
    print("🔒 Maintenant: NULL si pas trouvé, pas de fausses détections")

if __name__ == "__main__":
    process_all_categories_strict()
