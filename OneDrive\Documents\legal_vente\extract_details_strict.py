#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script STRICT pour extraire les détails - Met NULL si pas trouvé
Évite les fausses détections
"""

import json
import re
from typing import Optional

def extract_prix_strict(text: str) -> Optional[str]:
    """Extrait le prix de manière STRICTE - évite les fausses détections"""
    if not text:
        return None
    
    # D'abord, exclure les contextes qui ne sont PAS des prix
    exclusions_contexte = [
        'وصل عدد',  # Numéro de reçu
        'تسجيل عدد',  # Numéro d'enregistrement
        'بطاقة التعريف الوطنية عدد',  # Numéro de carte d'identité
        'الهاتف',  # Numéro de téléphone
        'الفاكس',  # Numéro de fax
        'المعرف الجبائي',  # Identifiant fiscal
        'رقم',  # Numéro
        'عدد الهاتف',  # Numéro de téléphone
        'phone', 'tel', 'fax'
    ]
    
    # Vérifier si le texte contient des exclusions
    text_lower = text.lower()
    for exclusion in exclusions_contexte:
        if exclusion in text_lower:
            # Si c'est un contexte d'exclusion, être plus strict
            pass
    
    # Patterns FLEXIBLES et COMPLETS pour les prix avec contexte obligatoire
    prix_patterns_stricts = [
        # Prix avec contexte arabe EXPLICITE de vente/prix
        r'(?:الثمن الإفتتاحي|الثمن الافتتاحي)\s*[:\s]*([0-9.,\s]+)\s*د',  # "الثمن الإفتتاحي : 192.850,864د"
        r'(?:الثمن الإفتتاحي|الثمن الافتتاحي)\s*[:\s]*[^(]*\(\s*([0-9.,\s]+)\s*د\s*\)',  # NOUVEAU: "الثمن الإفتتاحي: ... (155095,200 د)"
        r'(?:بثمن افتتاحي قدره|الثمن الافتتاحي|بالأمن الإفتتاحي|ثمن البيع|سعر البيع)\s*[:\s]*([0-9.,\s]+)',
        r'(?:بثمن افتتاحي قدره|الثمن الافتتاحي|بالأمن الإفتتاحي)\s*[^0-9]*\(([0-9.,\s]+)\)',

        # Prix avec mots arabes + parenthèses (PATTERN MANQUÉ PRINCIPAL)
        r'(?:مائة|ألف|آلاف|مليون)[^(]*دينار[^(]*\(\s*\*?\*?\s*([0-9.,\s]+)\s*\*?\*?\s*د\s*\)',  # NOUVEAU: "مائة و خمسة و خمسون ألف...دينارا...(155095,200 د)"
        r'(?:مائة|ألف|آلاف|مليون)[^(]*\(\s*\*?\*?\s*([0-9.,\s]+)\s*\*?\*?\s*(?:د|دينار|dinars?)\s*\)',

        # Prix avec contexte français EXPLICITE
        r'(?:mise à prix|prix de vente|prix d\'adjudication|montant de la vente)\s*[:\s]*([0-9.,\s]+)',
        r'(?:vendu pour|adjugé pour|cédé pour)\s*[:\s]*([0-9.,\s]+)',

        # Prix avec dinars EXPLICITES (formats variés)
        r'([0-9.,\s]+)\s*(?:dinars?|دينار|د)\s*(?:tunisiens?)?',
        r'\(\s*\*?\*?\s*([0-9.,\s]+)\s*\*?\*?\s*(?:د|دينار|dinars?)\s*\)',  # Entre parenthèses avec astérisques

        # Pattern spécifique pour le cas problématique (TESTÉ ET FONCTIONNEL)
        r'دينار[^(]*\([^)]*([0-9]{6,}[.,][0-9]+)[^)]*د[^)]*\)',  # Pattern pour "دينارا و 200 مليم. ( **155095,200** **د)"
        r'\(\s*\*?\*?\s*([0-9]{6,}[.,][0-9]+)\s*\*?\*?\s*د\s*\)',  # Pattern direct pour (155095,200 د)

        # Patterns de secours pour prix isolés avec contexte
        r'(?:ثمن|prix|montant|قيمة)[^0-9]*([0-9.,\s]{5,})',  # Au moins 5 caractères numériques
    ]
    
    for pattern in prix_patterns_stricts:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            prix = match if isinstance(match, str) else match
            prix_clean = re.sub(r'[^\d.,]', '', prix)
            
            # Validation stricte
            if prix_clean and len(prix_clean) >= 3:
                # Éviter les numéros de téléphone, ID, etc.
                if not re.match(r'^[0-9]{8,10}$', prix_clean):  # Pas de numéros de 8-10 chiffres
                    # Vérifier que ce n'est pas dans un contexte d'exclusion
                    if not is_in_exclusion_context(text, prix):
                        return prix_clean
    
    return None

def is_in_exclusion_context(text: str, prix: str) -> bool:
    """Vérifie si le prix est dans un contexte d'exclusion"""
    # Chercher le prix dans le texte et vérifier le contexte autour
    prix_escaped = re.escape(prix)
    
    # Patterns d'exclusion avec contexte
    exclusion_patterns = [
        f'وصل عدد[^0-9]*{prix_escaped}',
        f'تسجيل عدد[^0-9]*{prix_escaped}',
        f'بطاقة التعريف[^0-9]*{prix_escaped}',
        f'الهاتف[^0-9]*{prix_escaped}',
        f'المعرف الجبائي[^0-9]*{prix_escaped}',
        f'phone[^0-9]*{prix_escaped}',
        f'tel[^0-9]*{prix_escaped}',
    ]
    
    for pattern in exclusion_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            return True
    
    return False

def extract_ville_strict(text: str) -> Optional[str]:
    """Extrait la ville de manière STRICTE"""
    if not text:
        return None
    
    # Villes tunisiennes connues
    villes_tunisiennes = [
        'tunis', 'تونس', 'sfax', 'صفاقس', 'sousse', 'سوسة', 'kairouan', 'القيروان',
        'bizerte', 'بنزرت', 'gabès', 'قابس', 'ariana', 'أريانة', 'gafsa', 'قفصة',
        'monastir', 'المنستير', 'ben arous', 'بن عروس', 'kasserine', 'القصرين',
        'medenine', 'مدنين', 'nabeul', 'نابل', 'tataouine', 'تطاوين',
        'beja', 'باجة', 'jendouba', 'جندوبة', 'mahdia', 'المهدية',
        'manouba', 'منوبة', 'siliana', 'سليانة', 'tozeur', 'توزر',
        'zaghouan', 'زغوان', 'kef', 'الكاف', 'sidi bouzid', 'سيدي بوزيد',
        'kebili', 'قبلي', 'la marsa', 'المرسى', 'carthage', 'قرطاج',
        'بني حسان', 'جمال', 'أريانة', 'اريانة'
    ]
    
    text_lower = text.lower()
    
    # Chercher les villes directement
    for ville in villes_tunisiennes:
        if ville.lower() in text_lower:
            return ville.title()
    
    # Patterns stricts pour extraire les lieux
    lieu_patterns_stricts = [
        r'(?:القاطن|القاطنة|قاطن|قاطنة)\s+(?:ب|في)\s*([^،.\n]{3,30})',
        r'(?:كائن|الكائن)\s+(?:ب|في)\s*([^،.\n]{3,30})',
        r'(?:بالمحكمة الابتدائية|المحكمة الابتدائية)\s+(?:ب|في)?\s*([^،.\n]{3,30})',
        r'(?:tribunal|cour)\s+(?:de|d\'|du)\s*([^,.\n]{3,30})',
        r'(?:sis|situé)\s+(?:à|au|en)\s+([^,.\n]{3,30})',
    ]
    
    for pattern in lieu_patterns_stricts:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            lieu = match.strip()
            lieu = re.sub(r'[^\w\s\u0600-\u06FF]', '', lieu)
            if 3 <= len(lieu) <= 30:
                return lieu.title()
    
    return None

def extract_avocat_strict(text: str) -> Optional[str]:
    """Extrait l'avocat de manière STRICTE"""
    if not text:
        return None
    
    # Patterns stricts pour les avocats
    avocat_patterns_stricts = [
        r'نائبه\s+(?:الاستاذ|الأستاذ)\s+([^،.\n]{3,50})\s+(?:المحامي)',
        r'نائبته\s+(?:الاستاذة|الأستاذة)\s+([^،.\n]{3,50})\s+(?:المحامية)',
        r'(?:بمكتب محاميتهم|مكتب محاميتهم)\s+(?:الأستاذة|الأستاذ)\s+([^،.\n]{3,50})',
        r'(?:الأستاذة|الأستاذ)\s+([^،.\n]{3,50})\s+(?:المحامية|المحامي)\s+(?:لدى|ب)',
        r'(?:maître|me)\s+([^,.\n]{3,50})\s+(?:avocat|avocate)',
        r'(?:avocat|avocate)\s*:\s*(?:maître|me)?\s*([^,.\n]{3,50})',
    ]
    
    for pattern in avocat_patterns_stricts:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            avocat = match.strip()
            avocat = clean_avocat_name_strict(avocat)
            if avocat:
                return avocat
    
    return None

def clean_avocat_name_strict(avocat: str) -> Optional[str]:
    """Nettoie le nom de l'avocat de manière stricte"""
    if not avocat:
        return None
    
    # Normaliser les espaces
    avocat = re.sub(r'\s+', ' ', avocat)
    
    # Enlever les caractères spéciaux
    avocat = re.sub(r'[*\-_\(\)\[\]{}]+', '', avocat)
    
    # Enlever les suffixes
    avocat = re.sub(r'\s+(?:لدى|المحامية|المحامي|avocat|avocate).*', '', avocat)
    
    # Validation stricte
    avocat = avocat.strip()
    if 3 <= len(avocat) <= 50 and not re.match(r'^\d+$', avocat):
        # Éviter les mots génériques
        mots_generiques = ['الكائن', 'بعدد', 'شارع', 'نهج', 'الهاتف']
        if not any(mot in avocat.lower() for mot in mots_generiques):
            return avocat.title()
    
    return None

def extract_date_from_text(text: str) -> Optional[str]:
    """Extrait et normalise la date du texte"""
    if not text:
        return None

    # Dictionnaires de conversion des mois
    mois_arabes = {
        'جانفي': '01', 'فيفري': '02', 'مارس': '03', 'أفريل': '04',
        'ماي': '05', 'جوان': '06', 'جويلية': '07', 'أوت': '08',
        'سبتمبر': '09', 'أكتوبر': '10', 'نوفمبر': '11', 'ديسمبر': '12',
        'اوت': '08', 'جانفى': '01', 'فيفرى': '02'
    }

    mois_francais = {
        'janvier': '01', 'février': '02', 'mars': '03', 'avril': '04',
        'mai': '05', 'juin': '06', 'juillet': '07', 'août': '08',
        'septembre': '09', 'octobre': '10', 'novembre': '11', 'décembre': '12'
    }

    # Patterns pour extraire les dates
    date_patterns = [
        # Formats numériques
        r'(\d{1,2}/\d{1,2}/\d{4})',
        r'(\d{4}/\d{1,2}/\d{1,2})',
        r'(\d{1,2}-\d{1,2}-\d{4})',
        r'(\d{4}-\d{1,2}-\d{1,2})',

        # Formats arabes
        r'يوم\s+\w+\s+الموافق\s+[^\d]*(\d{1,2})\s+من\s+شهر\s+(\w+)\s+سنة\s+[^\d]*(\d{4})',
        r'(\d{1,2})\s+(\w+)\s+(\d{4})',
        r'\((\d{1,2})\)\s+(\w+)\s+(\d{4})',

        # Formats français
        r'(\d{1,2})\s+(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+(\d{4})',
        r'le\s+(\d{1,2})\s+(\w+)\s+(\d{4})',
    ]

    for pattern in date_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            match = matches[0]
            if isinstance(match, tuple):
                if len(match) == 2:
                    day, year = match
                    return f"{day.zfill(2)}/01/{year}"  # Mois par défaut
                elif len(match) == 3:
                    day, mois, year = match
                    mois_num = mois_arabes.get(mois.strip()) or mois_francais.get(mois.strip().lower()) or '01'
                    return f"{day.zfill(2)}/{mois_num}/{year}"
            else:
                # Format DD/MM/YYYY ou YYYY/MM/DD
                if '/' in match:
                    parts = match.split('/')
                    if len(parts) == 3:
                        if len(parts[0]) == 4:  # YYYY/MM/DD
                            return f"{parts[2].zfill(2)}/{parts[1].zfill(2)}/{parts[0]}"
                        else:  # DD/MM/YYYY
                            return f"{parts[0].zfill(2)}/{parts[1].zfill(2)}/{parts[2]}"
                return match

    return None

def extract_adresse_from_text(text: str) -> Optional[str]:
    """Extrait l'adresse exacte du texte"""
    if not text:
        return None

    # Patterns pour extraire l'adresse
    adresse_patterns = [
        # Patterns arabes
        r'كائنة\s+(?:بنهج|بشارع|ب)\s+([^،.\n]+?)(?:\s*[،.]|\s*مسيجة|\s*يحدها|\s*$)',
        r'كائن\s+(?:بع|بـ|ب|في|بنهج|بشارع)\s+([^،.\n]+?)(?:\s*[،.]|\s*و|\s*يحده|\s*$)',
        r'الكائن\s+(?:بـ|ب|في|بنهج|بشارع)\s+([^،.\n]+?)(?:\s*[،.]|\s*و|\s*$)',
        r'الرسم العقاري[^الكائن]*الكائن\s+(?:بـ|ب|في)\s+([^،.\n]+?)(?:\s*[،.]|\s*$)',
        r'(?:بـ|ب|في)\s*(\d+[^،.\n]*?(?:نهج|شارع|طريق|زنقة)[^،.\n]*?)(?:\s*[،.]|\s*و)',

        # Patterns français
        r'sis\s+(?:à|au|en)\s+([^,.\n]+?)(?:\s*[,.]|\s*$)',
        r'situé\s+(?:à|au|en)\s+([^,.\n]+?)(?:\s*[,.]|\s*$)',
        r'(?:au|à)\s+(\d+[^,.\n]*?(?:rue|avenue|boulevard)[^,.\n]*?)(?:\s*[,.]|\s*$)',
    ]

    for pattern in adresse_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            adresse = match.strip()
            # Nettoyer l'adresse
            adresse = re.sub(r'^[^\w\u0600-\u06FF]+', '', adresse)
            adresse = re.sub(r'[^\w\u0600-\u06FF\s]+$', '', adresse)
            adresse = re.sub(r'\s+', ' ', adresse)
            adresse = re.sub(r'\s+(?:و|et|and|مسيجة|بجدران|في|ارتفاع|يحدها|يحده).*$', '', adresse)

            if 3 <= len(adresse) <= 100:
                return adresse.strip()

    return None

def process_all_categories_strict():
    """Traite TOUTES les catégories avec logique stricte - REMPLACE les fichiers existants"""
    print("🔒 EXTRACTION STRICTE UNIVERSELLE - NULL SI PAS TROUVÉ")
    print("="*70)
    print("⚠️  ATTENTION: Remplace TOUS les fichiers existants avec logique stricte")
    print("="*70)

    categories = [
        ('articles_ventes_immobilieres_1000.json', 'Immobilier'),
        ('articles_fonds_de_commerce_1000.json', 'Fonds de Commerce'),
        ('articles_ventes_voitures_1000.json', 'Voitures'),
        ('articles_autres_actes_judiciaires_1000.json', 'Autres Actes'),
        ('articles_immobiliers_ultra_complet.json', 'Immobilier Ultra-Complet')
    ]
    
    for filename, category_name in categories:
        print(f"\n📋 TRAITEMENT: {category_name}")
        print("-" * 40)
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                articles = json.load(f)
            print(f"✅ Chargé {len(articles)} articles")
        except Exception as e:
            print(f"❌ Erreur: {e}")
            continue
        
        if not articles:
            print("⚠️ Aucun article à traiter")
            continue
        
        # Statistiques
        stats = {'prix': 0, 'ville': 0, 'avocat': 0, 'date': 0, 'adresse': 0, 'total': len(articles)}
        
        for article in articles:
            source = article.get('_source', {})
            article_text = source.get('articleText', '')

            # Extraire avec logique stricte
            prix = extract_prix_strict(article_text)
            ville = extract_ville_strict(article_text)
            avocat = extract_avocat_strict(article_text)

            # AJOUTER L'EXTRACTION DE DATE ET ADRESSE
            date_vente = extract_date_from_text(article_text)
            adresse_exacte = extract_adresse_from_text(article_text)

            # Mettre à jour TOUS les champs (NULL si pas trouvé)
            source['prix'] = prix
            source['ville'] = ville
            source['avocat'] = avocat
            source['date_vente'] = date_vente
            source['adresse_exacte'] = adresse_exacte
            
            # Compter les extractions réussies
            if prix: stats['prix'] += 1
            if ville: stats['ville'] += 1
            if avocat: stats['avocat'] += 1
            if date_vente: stats['date'] += 1
            if adresse_exacte: stats['adresse'] += 1
        
        # Afficher les statistiques
        total = stats['total']
        print(f"💰 Prix extraits: {stats['prix']}/{total} ({stats['prix']/total*100:.1f}%)")
        print(f"🏙️ Villes extraites: {stats['ville']}/{total} ({stats['ville']/total*100:.1f}%)")
        print(f"⚖️ Avocats extraits: {stats['avocat']}/{total} ({stats['avocat']/total*100:.1f}%)")
        print(f"📅 Dates extraites: {stats['date']}/{total} ({stats['date']/total*100:.1f}%)")
        print(f"🏠 Adresses extraites: {stats['adresse']}/{total} ({stats['adresse']/total*100:.1f}%)")
        
        # Sauvegarder - REMPLACE le fichier original avec version stricte
        if 'ultra_complet' in filename:
            # Pour le fichier ultra-complet, créer une nouvelle version stricte
            output_filename = 'articles_immobiliers_final_strict.json'
        else:
            # Pour les autres, remplacer directement
            output_filename = filename

        try:
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)
            print(f"✅ Sauvegardé (REMPLACÉ): {output_filename}")
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")

    print("\n" + "="*70)
    print("🎉 Extraction stricte universelle terminée!")
    print("📁 TOUS les fichiers ont été mis à jour avec la logique stricte")
    print("🔒 Maintenant: NULL si pas trouvé, pas de fausses détections")

if __name__ == "__main__":
    process_all_categories_strict()
