#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script complet pour extraire et catégoriser TOUS les articles depuis articles.json
"""

import json
import re
from typing import Dict, List, Any

def is_fonds_commerce(text: str) -> bool:
    """Vérifie si c'est un fonds de commerce (priorité absolue)"""
    if not text:
        return False
    
    text_lower = text.lower()
    
    fonds_commerce_markers = [
        'أصل تجاري', 'وكالة حرة', 'كراء أصل تجاري', 'تسويغ أصل تجاري',
        'بيع أصل تجاري', 'تجديد وكالة حرة', 'فسخ عقد كراء أصل تجاري',
        'إشهار بفسخ', 'عناصر مادية ومعنوية', 'استغلال تجاري', 'نشاط تجاري',
        'fonds de commerce', 'fond commercial', 'location gérance', 'bail commercial',
        'cession de fonds', 'exploitation commerciale'
    ]
    
    return any(marker in text_lower for marker in fonds_commerce_markers)

def is_vente_voiture_reelle(text: str) -> bool:
    """Vérifie si c'est une VRAIE vente de véhicule (pas un garage/atelier)"""
    if not text:
        return False
    
    text_lower = text.lower()
    
    # Marqueurs de vraies ventes de véhicules
    vente_vehicule_markers = [
        'بيع سيارة', 'بيع مركبة', 'بيع عربة', 'مزاد سيارات', 'مزايدة سيارات',
        'بيع نافير', 'بيع شالوتير', 'مزاد مركبات', 'مزايدة مركبات',
        'vente de voiture', 'vente véhicule', 'vente automobile', 'enchères voiture',
        'vente de navire', 'vente chalutier', 'adjudication véhicule'
    ]
    
    # Exclusions : ateliers, garages, magasins (ce sont des immeubles)
    exclusions_immobilier = [
        'atelier de', 'garage', 'magasin', 'local', 'immeuble', 'bâtiment',
        'ورشة', 'محل', 'مرآب', 'مبنى', 'عقار'
    ]
    
    # Vérifier s'il y a des marqueurs de vente de véhicules
    has_vehicle_sale = any(marker in text_lower for marker in vente_vehicule_markers)
    
    # Vérifier s'il y a des exclusions immobilières
    has_immobilier_exclusion = any(exclusion in text_lower for exclusion in exclusions_immobilier)
    
    return has_vehicle_sale and not has_immobilier_exclusion

def is_vente_immobiliere(text: str) -> bool:
    """Vérifie si c'est une vraie vente immobilière"""
    if not text:
        return False
    
    text_lower = text.lower()
    
    immobilier_markers = [
        'بيع عقار', 'بيع منزل', 'بيع شقة', 'بيع فيلا', 'بيع أرض', 'بيع دار',
        'مزاد عقاري', 'مزايدة عقارية', 'بيع بالمزاد العلني', 'سايسة عقارية',
        'عقلة عقارية', 'تبتيت عقار', 'رسم عقاري', 'ملكية عقارية',
        'vente immobilière', 'saisie immobilière', 'enchères immobilières',
        'vente aux enchères publiques', 'adjudication', 'mise à prix',
        'titre foncier', 'propriété immobilière', 'immeuble à vendre',
        'bien immobilier', 'terrain à vendre', 'maison à vendre',
        'appartement à vendre', 'villa à vendre'
    ]
    
    description_immobilier = [
        'متر مربع', 'مساحة', 'طابق', 'غرفة', 'صالة', 'مطبخ', 'حمام',
        'superficie', 'chambre', 'salon', 'cuisine', 'salle de bain',
        'rez-de-chaussée', 'étage', 'terrasse', 'balcon', 'jardin'
    ]
    
    has_immobilier_marker = any(marker in text_lower for marker in immobilier_markers)
    has_description = any(desc in text_lower for desc in description_immobilier)
    
    return has_immobilier_marker or has_description

def categorize_article(text: str) -> str:
    """Catégorise un article selon son contenu"""
    if not text:
        return 'autres'
    
    # Priorité 1: Fonds de commerce
    if is_fonds_commerce(text):
        return 'fonds_commerce'
    
    # Priorité 2: Ventes de véhicules RÉELLES
    if is_vente_voiture_reelle(text):
        return 'voiture'
    
    # Priorité 3: Ventes immobilières
    if is_vente_immobiliere(text):
        return 'immobilier'
    
    return 'autres'

def extract_all_articles_from_json() -> List[Dict[str, Any]]:
    """Extrait TOUS les articles du fichier articles.json"""
    print("📖 Lecture du fichier articles.json...")
    
    try:
        with open('articles.json', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✅ Fichier lu avec succès")
        
        # Essayer de charger directement comme JSON
        try:
            articles = json.loads(content)
            print(f"✅ JSON valide - {len(articles)} articles chargés")
            return articles
        except json.JSONDecodeError:
            print("⚠️ JSON invalide, parsing manuel...")
            return parse_articles_manually(content)
            
    except Exception as e:
        print(f"❌ Erreur lors de la lecture: {e}")
        return []

def parse_articles_manually(content: str) -> List[Dict[str, Any]]:
    """Parse manuellement les articles du contenu"""
    articles = []
    
    # Diviser le contenu en lignes
    lines = content.split('\n')
    current_article_lines = []
    brace_count = 0
    in_article = False
    
    for line_num, line in enumerate(lines):
        stripped = line.strip()
        
        # Détecter le début d'un article
        if stripped.startswith('{') and not in_article:
            in_article = True
            current_article_lines = [line]
            brace_count = line.count('{') - line.count('}')
        elif in_article:
            current_article_lines.append(line)
            brace_count += line.count('{') - line.count('}')
            
            # Fin de l'article
            if brace_count <= 0:
                article_text = '\n'.join(current_article_lines)
                
                try:
                    # Nettoyer les guillemets triples
                    cleaned_text = clean_article_text(article_text)
                    article = json.loads(cleaned_text)
                    articles.append(article)
                    
                except Exception as e:
                    # Si le JSON échoue, extraire manuellement
                    try:
                        article = extract_article_manually(article_text)
                        if article and article.get('_source', {}).get('articleText'):
                            articles.append(article)
                    except:
                        print(f"⚠️ Article ignoré à la ligne {line_num}")
                
                current_article_lines = []
                in_article = False
                brace_count = 0
    
    print(f"✅ Parsing manuel terminé: {len(articles)} articles extraits")
    return articles

def clean_article_text(article_text: str) -> str:
    """Nettoie le texte d'un article pour le parsing JSON"""
    # Remplacer les guillemets triples par des guillemets simples
    def replace_triple_quotes(match):
        text = match.group(1)
        text = text.replace(chr(10), " ").replace(chr(13), " ")
        text = text.replace("\\", "\\\\").replace('"', '\\"')
        return f'"{text}"'

    cleaned = re.sub(r'"""([^"]*?)"""', replace_triple_quotes, article_text, flags=re.DOTALL)
    return cleaned

def extract_article_manually(article_text: str) -> Dict[str, Any]:
    """Extrait manuellement les champs d'un article"""
    article = {}
    
    # Extraire les champs principaux
    index_match = re.search(r'"_index":\s*"([^"]*)"', article_text)
    if index_match:
        article['_index'] = index_match.group(1)
    
    id_match = re.search(r'"_id":\s*"([^"]*)"', article_text)
    if id_match:
        article['_id'] = id_match.group(1)
    
    score_match = re.search(r'"_score":\s*([0-9.]+)', article_text)
    if score_match:
        article['_score'] = float(score_match.group(1))
    
    # Extraire _source
    source = {}
    
    # articleText (le plus important)
    text_match = re.search(r'"articleText":\s*(?:"""([^"]*?)"""|"([^"]*)")', article_text, re.DOTALL)
    if text_match:
        source['articleText'] = text_match.group(1) or text_match.group(2)
    
    # Autres champs essentiels
    for field in ['doc_id', 'title', 'reference', 'lang', 'publishedAt', 'file', 'source']:
        field_match = re.search(f'"{field}":\\s*"([^"]*)"', article_text)
        if field_match:
            source[field] = field_match.group(1)
    
    # source_grps
    source_grps_match = re.search(r'"source_grps":\s*\[(.*?)\]', article_text, re.DOTALL)
    if source_grps_match:
        grps_content = source_grps_match.group(1)
        grps = re.findall(r'"([^"]*)"', grps_content)
        source['source_grps'] = grps
    
    # categories (structure simplifiée)
    source['categories'] = []
    
    # page et extras
    source['page'] = None
    source['extras'] = None
    
    article['_source'] = source
    return article

def main():
    """Fonction principale"""
    print("🔍 Début de l'extraction et catégorisation complète...")
    
    # Extraire tous les articles
    articles = extract_all_articles_from_json()
    if not articles:
        print("❌ Aucun article extrait")
        return
    
    # Catégoriser
    categories = {
        'immobilier': [],
        'voiture': [],
        'fonds_commerce': [],
        'autres': []
    }
    
    print("📋 Catégorisation en cours...")
    for i, article in enumerate(articles):
        try:
            article_text = article.get('_source', {}).get('articleText', '')
            category = categorize_article(article_text)
            categories[category].append(article)
            
            if (i + 1) % 100 == 0:
                print(f"  Traité {i + 1}/{len(articles)} articles...")
                
        except Exception as e:
            categories['autres'].append(article)
    
    # Statistiques
    total = len(articles)
    print("\n" + "="*60)
    print("📊 STATISTIQUES FINALES")
    print("="*60)
    print(f"📄 Total des articles: {total}")
    print(f"🏠 Ventes immobilières: {len(categories['immobilier'])} ({len(categories['immobilier'])/total*100:.1f}%)")
    print(f"🚗 Ventes de voitures: {len(categories['voiture'])} ({len(categories['voiture'])/total*100:.1f}%)")
    print(f"🏪 Fonds de commerce: {len(categories['fonds_commerce'])} ({len(categories['fonds_commerce'])/total*100:.1f}%)")
    print(f"⚖️ Autres actes judiciaires: {len(categories['autres'])} ({len(categories['autres'])/total*100:.1f}%)")
    print("="*60)
    
    # Afficher quelques exemples de chaque catégorie
    for cat_name, articles_list in categories.items():
        if articles_list and cat_name != 'autres':
            print(f"\n📝 Exemples de {cat_name}:")
            for i, article in enumerate(articles_list[:2]):
                text = article.get('_source', {}).get('articleText', '')[:150]
                print(f"  {i+1}. {text}...")
    
    # Sauvegarder
    category_names = {
        'immobilier': 'ventes_immobilieres_final',
        'voiture': 'ventes_voitures_final',
        'fonds_commerce': 'fonds_de_commerce_final',
        'autres': 'autres_actes_judiciaires_final'
    }
    
    print("\n💾 Sauvegarde des fichiers...")
    for category, articles_list in categories.items():
        filename = f"articles_{category_names[category]}.json"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(articles_list, f, ensure_ascii=False, indent=2)
            print(f"✅ Sauvegardé {len(articles_list)} articles dans {filename}")
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde de {filename}: {e}")
    
    print("✅ Extraction et catégorisation terminées avec succès!")

if __name__ == "__main__":
    main()
