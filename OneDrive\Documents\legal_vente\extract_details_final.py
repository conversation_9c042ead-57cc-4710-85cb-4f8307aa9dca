#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Extraction finale des détails des articles immobiliers
"""

import json
import re
from typing import Optional

def extract_date_vente(text: str) -> Optional[str]:
    """Extrait la date de vente"""
    if not text:
        return None

    # Patterns pour différents formats de dates
    date_patterns = [
        # Formats numériques
        r'(\d{1,2}/\d{1,2}/\d{4})',  # 24/04/2023
        r'(\d{4}/\d{1,2}/\d{1,2})',  # 2023/04/24
        r'(\d{1,2}-\d{1,2}-\d{4})',  # 24-04-2023
        r'(\d{4}-\d{1,2}-\d{1,2})',  # 2023-04-24
        r'(\d{1,2}\s+\w+\s+\d{4})',  # 24 avril 2023

        # Formats arabes détaillés
        r'يوم\s+\w+\s+الموافق\s+[^\d]*(\d{1,2})\s+من\s+شهر\s+\w+\s+سنة\s+[^\d]*(\d{4})',  # Format arabe complet
        r'الموافق\s+[^\d]*(\d{1,2})\s+من\s+شهر\s+(\w+)\s+سنة\s+[^\d]*(\d{4})',  # Format arabe simplifié
        r'(\d{1,2})\s+(\w+)\s+(\d{4})',  # 2 أوت 2023
        r'\((\d{1,2})\)\s+(\w+)\s+(\d{4})',  # (02) اوت 2023

        # Formats français
        r'(\d{1,2})\s+(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+(\d{4})',
        r'le\s+(\d{1,2})\s+(\w+)\s+(\d{4})',
    ]

    for pattern in date_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            if isinstance(matches[0], tuple):
                if len(matches[0]) == 2:
                    return f"{matches[0][0]}/{matches[0][1]}"
                elif len(matches[0]) == 3:
                    return f"{matches[0][0]}/{matches[0][1]}/{matches[0][2]}"
            else:
                return matches[0]

    return None

def extract_prix(text: str) -> Optional[str]:
    """Extrait le prix"""
    if not text:
        return None

    # Patterns pour les prix avec contexte
    prix_patterns = [
        # Patterns avec contexte arabe
        r'(?:الثمن الإفتتاحي|الأمن الإفتتاحي|بالأمن الإفتتاحي)[^:]*[:]*\s*([0-9.,]+)',
        r'(?:مائة|ألف|آلاف|مليون)\s+[^(]*\(([0-9.,]+)\)',  # مائة و ستة آلاف (106.590.000)
        r'(?:ثمن|الثمن|قدره|بمبلغ)[:\s]*([0-9.,]+)',

        # Patterns avec contexte français
        r'(?:mise à prix|prix|fixée? à|montant)[:\s]*([0-9.,]+)',
        r'(?:dinars?|دينار|د|DT|dt)[:\s]*([0-9.,]+)',
        r'([0-9.,]+)\s*(?:dinars?|دينار|د|DT|dt)',

        # Patterns numériques purs
        r'([0-9]+[.,][0-9]+[.,][0-9]+)',  # Format 123.456.789
        r'([0-9]+\.[0-9]+)',  # Format 123.456
        r'\(([0-9.,]+)\)',  # Entre parenthèses
        r'([0-9]{4,})',  # Au moins 4 chiffres
    ]

    for pattern in prix_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            prix = match.group(1) if len(match.groups()) > 0 else match.group(0)
            # Nettoyer et valider
            prix_clean = re.sub(r'[^\d.,]', '', prix)
            if prix_clean and len(prix_clean) >= 3:
                return prix_clean

    return None

def extract_ville(text: str) -> Optional[str]:
    """Extrait la ville"""
    if not text:
        return None

    # Villes tunisiennes avec variantes
    villes_tunisiennes = [
        'tunis', 'sfax', 'sousse', 'kairouan', 'bizerte', 'gabès', 'ariana', 'gafsa',
        'monastir', 'ben arous', 'kasserine', 'medenine', 'nabeul', 'tataouine',
        'beja', 'jendouba', 'mahdia', 'manouba', 'siliana', 'tozeur', 'zaghouan',
        'kef', 'sidi bouzid', 'kebili', 'la marsa', 'carthage', 'hammam lif',
        'grombalia', 'hammamet', 'klibia', 'korba', 'menzel bourguiba',
        'المنستير', 'صفاقس', 'تونس', 'سوسة', 'القيروان', 'بنزرت', 'قابس', 'أريانة',
        'منوبة', 'نابل', 'بن عروس', 'المهدية', 'جندوبة', 'باجة', 'منوية'
    ]

    text_lower = text.lower()

    # Chercher les villes directement
    for ville in villes_tunisiennes:
        if ville.lower() in text_lower:
            return ville.title()

    # Patterns pour extraire les lieux avec contexte
    lieu_patterns = [
        # Patterns arabes
        r'(?:بالمحكمة الابتدائية|المحكمة الابتدائية)\s+(?:ب|في)?\s*([^،.\n]{3,30})',
        r'(?:محكمة|المحكمة)\s+(?:الابتدائية\s+)?(?:ب|في|de|d\'|du)?\s*([^،.\n]{3,30})',
        r'(?:كائن|الكائن)\s+(?:ب|في|بعدد)\s+[^،]*?([^،.\n]{3,30})',
        r'(?:ولاية|محافظة)\s+([^،.\n]{3,30})',

        # Patterns français
        r'(?:tribunal|محكمة)\s+(?:de|d\'|du)?\s*([^,.\n]{3,30})',
        r'(?:gouvernorat|ولاية)\s+(?:de|d\'|du)?\s*([^,.\n]{3,30})',
        r'(?:sis|situé|كائن)\s+(?:à|au|en|par|في|ب)\s+([^,.\n]{3,30})',

        # Patterns pour adresses
        r'(?:شارع|نهج|rue|avenue)\s+[^،]*?([^،.\n]{3,30})',
    ]

    for pattern in lieu_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            lieu = match.group(1).strip()
            # Nettoyer le lieu
            lieu = re.sub(r'[^\w\s\u0600-\u06FF]', '', lieu)  # Garder lettres arabes/latines
            if 3 <= len(lieu) <= 30:
                return lieu.title()

    return None

def extract_avocat(text: str) -> Optional[str]:
    """Extrait le nom de l'avocat"""
    if not text:
        return None

    # Patterns pour les avocats avec contexte
    avocat_patterns = [
        # Patterns arabes détaillés
        r'(?:بمكتب محاميتهم|مكتب محاميتهم)\s+(?:الأستاذة|الأستاذ|الاستاذة|الاستاذ)\s+([^،.\n]{3,50})',
        r'(?:الأستاذة|الأستاذ|الاستاذة|الاستاذ)\s+([^،.\n]{3,50})\s+(?:المحامية|المحامي)',
        r'(?:الأستاذة|الأستاذ|الاستاذة|الاستاذ)\s+([^،.\n]{3,50})\s+(?:لدى|المحامية لدى|المحامي لدى)',
        r'مكتب\s+(?:الأستاذة|الأستاذ|الاستاذة|الاستاذ)\s+([^،.\n]{3,50})',

        # Patterns français
        r'(?:maître|maître)\s+([^,.\n]{3,50})\s+(?:avocat|avocate)',
        r'(?:me|m\.)\s+([^,.\n]{3,50})',
        r'cabinet\s+(?:maître|maître)\s+([^,.\n]{3,50})',
        r'avocat[:\s]+(?:maître|maître)?\s*([^,.\n]{3,50})',

        # Patterns génériques
        r'(?:الأستاذة|الأستاذ|الاستاذة|الاستاذ)\s+([^،.\n]{3,50})',
        r'(?:maître|maître)\s+([^,.\n]{3,50})',
    ]

    for pattern in avocat_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            avocat = match.group(1).strip()
            # Nettoyer le nom
            avocat = re.sub(r'\s+', ' ', avocat)
            avocat = re.sub(r'[*\-_\(\)]+', '', avocat)  # Enlever les caractères spéciaux
            avocat = re.sub(r'\s+(?:لدى|المحامية|المحامي|avocat|avocate).*', '', avocat)  # Enlever les suffixes

            # Valider la longueur et le contenu
            if 3 <= len(avocat) <= 50 and not re.match(r'^\d+$', avocat):  # Pas que des chiffres
                return avocat.strip().title()

    return None

def process_all_articles():
    """Traite tous les articles"""
    print("🏠 EXTRACTION DES DÉTAILS DES ARTICLES IMMOBILIERS")
    print("="*60)
    
    # Charger le fichier
    try:
        with open('articles_ventes_immobilieres_1000.json', 'r', encoding='utf-8') as f:
            articles = json.load(f)
        print(f"✅ Chargé {len(articles)} articles immobiliers")
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return
    
    # Traiter les articles
    processed_articles = []
    stats = {'date': 0, 'prix': 0, 'ville': 0, 'avocat': 0}
    
    print("\n🔍 Extraction en cours...")
    
    for i, article in enumerate(articles):
        # Copier l'article
        new_article = article.copy()
        text = article.get('_source', {}).get('articleText', '')
        
        # Extraire les détails
        date_vente = extract_date_vente(text)
        prix = extract_prix(text)
        ville = extract_ville(text)
        avocat = extract_avocat(text)
        
        # Ajouter les champs
        if '_source' not in new_article:
            new_article['_source'] = {}
        
        new_article['_source']['date_vente'] = date_vente
        new_article['_source']['prix'] = prix
        new_article['_source']['ville'] = ville
        new_article['_source']['avocat'] = avocat
        
        # Compter les extractions réussies
        if date_vente: stats['date'] += 1
        if prix: stats['prix'] += 1
        if ville: stats['ville'] += 1
        if avocat: stats['avocat'] += 1
        
        processed_articles.append(new_article)
        
        # Afficher le progrès
        if (i + 1) % 100 == 0:
            print(f"  Traité {i + 1}/{len(articles)} articles...")
    
    # Statistiques
    total = len(articles)
    print("\n" + "="*60)
    print("📊 STATISTIQUES D'EXTRACTION")
    print("="*60)
    print(f"📄 Total des articles: {total}")
    print(f"📅 Dates extraites: {stats['date']} ({stats['date']/total*100:.1f}%)")
    print(f"💰 Prix extraits: {stats['prix']} ({stats['prix']/total*100:.1f}%)")
    print(f"🏙️ Villes extraites: {stats['ville']} ({stats['ville']/total*100:.1f}%)")
    print(f"⚖️ Avocats extraits: {stats['avocat']} ({stats['avocat']/total*100:.1f}%)")
    
    # Afficher quelques exemples
    print("\n📝 EXEMPLES D'EXTRACTION:")
    for i in range(min(5, len(processed_articles))):
        article = processed_articles[i]
        source = article.get('_source', {})
        print(f"\nArticle {i+1} (ID: {article.get('_id', 'N/A')}):")
        print(f"  📅 Date: {source.get('date_vente', 'Non trouvée')}")
        print(f"  💰 Prix: {source.get('prix', 'Non trouvé')}")
        print(f"  🏙️ Ville: {source.get('ville', 'Non trouvée')}")
        print(f"  ⚖️ Avocat: {source.get('avocat', 'Non trouvé')}")
    
    # Sauvegarder
    output_filename = 'articles_immobiliers_avec_details.json'
    try:
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(processed_articles, f, ensure_ascii=False, indent=2)
        print(f"\n✅ Fichier enrichi sauvegardé: {output_filename}")
    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde: {e}")
    
    print("="*60)
    print("✅ Extraction terminée!")

if __name__ == "__main__":
    process_all_articles()
