#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script amélioré et flexible pour extraire tous les détails des articles immobiliers
"""

import json
import re
from typing import Optional, List, Dict

def extract_prix_flexible(text: str) -> Optional[str]:
    """Extrait le prix de manière flexible"""
    if not text:
        return None
    
    # Patterns pour les prix avec priorité
    prix_patterns = [
        # Pattern 1: Prix entre parenthèses avec "د" (priorité haute)
        r'\(([0-9.,]+)د\)',
        
        # Pattern 2: Prix avec contexte arabe explicite
        r'(?:بثمن افتتاحي قدره|الثمن الافتتاحي|بالأمن الإفتتاحي)[^(]*\(([0-9.,]+)',
        r'(?:بثمن افتتاحي قدره|الثمن الافتتاحي|بالأمن الإفتتاحي)[:\s]*([0-9.,]+)',
        
        # Pattern 3: Prix avec mots arabes + chiffres entre parenthèses
        r'(?:مائة|ألف|آلاف|مليون)[^(]*\(([0-9.,]+)\)',
        
        # Pattern 4: Prix avec contexte français
        r'(?:mise à prix|prix|fixée? à|montant)[:\s]*([0-9.,]+)',
        
        # Pattern 5: Prix avec dinars
        r'([0-9.,]+)\s*(?:dinars?|دينار|د|DT|dt)',
        
        # Pattern 6: Formats numériques purs (priorité basse)
        r'([0-9]+[.,][0-9]+[.,][0-9]+)',  # Format 123.456.789
        r'([0-9]{5,})',  # Au moins 5 chiffres
    ]
    
    for pattern in prix_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            prix = match.group(1)
            # Nettoyer et valider
            prix_clean = re.sub(r'[^\d.,]', '', prix)
            if prix_clean and len(prix_clean) >= 3:
                return prix_clean
    
    return None

def extract_ville_flexible(text: str) -> Optional[str]:
    """Extrait la ville de manière flexible"""
    if not text:
        return None
    
    # Villes tunisiennes étendues
    villes_tunisiennes = [
        'tunis', 'sfax', 'sousse', 'kairouan', 'bizerte', 'gabès', 'ariana', 'gafsa',
        'monastir', 'ben arous', 'kasserine', 'medenine', 'nabeul', 'tataouine',
        'beja', 'jendouba', 'mahdia', 'manouba', 'siliana', 'tozeur', 'zaghouan',
        'kef', 'sidi bouzid', 'kebili', 'la marsa', 'carthage', 'hammam lif',
        'grombalia', 'hammamet', 'klibia', 'korba', 'menzel bourguiba', 'جمال',
        'بني حسان', 'المنستير', 'صفاقس', 'تونس', 'سوسة', 'القيروان', 'بنزرت', 
        'قابس', 'أريانة', 'منوبة', 'نابل', 'بن عروس', 'المهدية', 'جندوبة', 'باجة'
    ]
    
    text_lower = text.lower()
    
    # Chercher les villes directement dans le texte
    for ville in villes_tunisiennes:
        if ville.lower() in text_lower:
            return ville.title()
    
    # Patterns pour extraire les lieux avec contexte
    lieu_patterns = [
        # Patterns arabes spécifiques
        r'(?:قاطن|كائن|الكائن)\s+(?:ب|في|ببـ)\s*([^،.\n]{3,30})',
        r'(?:بالمحكمة الابتدائية|المحكمة الابتدائية)\s+(?:ب|في)?\s*([^،.\n]{3,30})',
        r'(?:العدل المنفذ)\s+(?:ب|في)\s*([^،.\n]{3,30})',
        
        # Patterns français
        r'(?:tribunal|محكمة)\s+(?:de|d\'|du)?\s*([^,.\n]{3,30})',
        r'(?:sis|situé)\s+(?:à|au|en)\s+([^,.\n]{3,30})',
    ]
    
    for pattern in lieu_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            lieu = match.group(1).strip()
            # Nettoyer le lieu
            lieu = re.sub(r'[^\w\s\u0600-\u06FF]', '', lieu)
            if 3 <= len(lieu) <= 30:
                return lieu.title()
    
    return None

def extract_adresse_flexible(text: str) -> Optional[str]:
    """Extrait l'adresse de manière flexible"""
    if not text:
        return None
    
    # Patterns pour extraire l'adresse avec priorité
    adresse_patterns = [
        # Pattern 1: Adresse avec "كائنة بنهج" (priorité haute)
        r'كائنة\s+(?:بنهج|بشارع|ب)\s+([^،.\n]+?)(?:\s*[،.]|\s*$)',
        
        # Pattern 2: "الكائن بـ" avec adresse complète
        r'الكائن\s+(?:بـ|ب|في)\s+([^،.\n]+?)(?:\s*[،.]|\s*و)',
        
        # Pattern 3: "كائن بع" (variante)
        r'كائن\s+(?:بع|بـ|ب|في)\s+([^،.\n]+?)(?:\s*[،.]|\s*و)',
        
        # Pattern 4: Adresse avec numéro et rue
        r'(?:بـ|ب|في)\s*(\d+[^،.\n]*?(?:نهج|شارع|طريق)[^،.\n]*?)(?:\s*[،.]|\s*و)',
        
        # Pattern 5: Patterns français
        r'sis\s+(?:à|au|en)\s+([^,.\n]+?)(?:\s*[,.]|\s*$)',
        r'situé\s+(?:à|au|en)\s+([^,.\n]+?)(?:\s*[,.]|\s*$)',
        
        # Pattern 6: Avec renseignements cadastraux
        r'الرسم العقاري[^الكائن]*الكائن\s+(?:بـ|ب|في)\s+([^،.\n]+?)(?:\s*[،.]|\s*$)',
    ]
    
    for pattern in adresse_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            adresse = match.group(1).strip()
            
            # Nettoyer l'adresse
            adresse = clean_adresse_flexible(adresse)
            
            # Valider l'adresse
            if is_valid_adresse_flexible(adresse):
                return adresse
    
    return None

def clean_adresse_flexible(adresse: str) -> str:
    """Nettoie l'adresse de manière flexible"""
    if not adresse:
        return ""
    
    # Enlever les caractères indésirables
    adresse = re.sub(r'^[^\w\u0600-\u06FF]+', '', adresse)
    adresse = re.sub(r'[^\w\u0600-\u06FF\s]+$', '', adresse)
    
    # Normaliser les espaces
    adresse = re.sub(r'\s+', ' ', adresse)
    
    # Enlever les mots de liaison en fin
    adresse = re.sub(r'\s+(?:و|et|and|مسيجة|بجدران|في|ارتفاع).*$', '', adresse)
    
    return adresse.strip()

def is_valid_adresse_flexible(adresse: str) -> bool:
    """Valide l'adresse de manière flexible"""
    if not adresse or len(adresse) < 3:
        return False
    
    # Indicateurs d'adresse valide
    indicateurs_valides = [
        'نهج', 'شارع', 'طريق', 'زنقة', 'حي', 'دوار', 'منطقة',
        'rue', 'avenue', 'boulevard', 'place', 'impasse', 'chemin'
    ]
    
    # L'adresse doit contenir au moins un indicateur OU un numéro
    has_numero = re.search(r'\d+', adresse)
    has_indicateur = any(ind in adresse.lower() for ind in indicateurs_valides)
    
    if not (has_numero or has_indicateur):
        return False
    
    # Exclure les adresses trop génériques ou incorrectes
    exclusions = [
        'الهاتف', 'الفاكس', 'phone', 'fax', 'email', 'المعرف', 'الجبائي'
    ]
    
    if any(excl in adresse.lower() for excl in exclusions):
        return False
    
    return True

def extract_avocat_flexible(text: str) -> Optional[str]:
    """Extrait l'avocat de manière flexible"""
    if not text:
        return None
    
    # Patterns pour les avocats avec priorité
    avocat_patterns = [
        # Pattern 1: "نائبه الاستاذ [nom] المحامي" (priorité haute)
        r'نائبه\s+(?:الاستاذ|الأستاذ)\s+([^،.\n]{3,50})\s+المحامي',
        
        # Pattern 2: Patterns classiques
        r'(?:بمكتب محاميتهم|مكتب محاميتهم)\s+(?:الأستاذة|الأستاذ|الاستاذة|الاستاذ)\s+([^،.\n]{3,50})',
        r'(?:الأستاذة|الأستاذ|الاستاذة|الاستاذ)\s+([^،.\n]{3,50})\s+(?:المحامية|المحامي)',
        
        # Pattern 3: Patterns français
        r'(?:maître|maître)\s+([^,.\n]{3,50})\s+(?:avocat|avocate)',
        r'(?:me|m\.)\s+([^,.\n]{3,50})',
        
        # Pattern 4: Patterns génériques
        r'مكتب\s+(?:الأستاذة|الأستاذ|الاستاذة|الاستاذ)\s+([^،.\n]{3,50})',
    ]
    
    for pattern in avocat_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            avocat = match.group(1).strip()
            # Nettoyer le nom
            avocat = clean_avocat_name(avocat)
            if avocat:
                return avocat
    
    return None

def clean_avocat_name(avocat: str) -> Optional[str]:
    """Nettoie le nom de l'avocat"""
    if not avocat:
        return None
    
    # Normaliser les espaces
    avocat = re.sub(r'\s+', ' ', avocat)
    
    # Enlever les caractères spéciaux
    avocat = re.sub(r'[*\-_\(\)]+', '', avocat)
    
    # Enlever les suffixes
    avocat = re.sub(r'\s+(?:لدى|المحامية|المحامي|avocat|avocate).*', '', avocat)
    
    # Valider
    if 3 <= len(avocat) <= 50 and not re.match(r'^\d+$', avocat):
        return avocat.strip().title()
    
    return None

def process_articles_improved():
    """Traite tous les articles avec la logique améliorée"""
    print("🔧 EXTRACTION AMÉLIORÉE ET FLEXIBLE")
    print("="*60)
    
    # Charger le fichier
    try:
        with open('articles_immobiliers_dates_normalisees.json', 'r', encoding='utf-8') as f:
            articles = json.load(f)
        print(f"✅ Chargé {len(articles)} articles")
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return
    
    # Statistiques
    stats = {
        'prix_corriges': 0,
        'ville_corrigee': 0,
        'adresse_ajoutee': 0,
        'avocat_corrige': 0
    }
    
    exemples = []
    
    print("\n🔄 Retraitement avec logique améliorée...")
    
    for i, article in enumerate(articles):
        source = article.get('_source', {})
        article_text = source.get('articleText', '')
        
        # Sauvegarder les anciennes valeurs
        ancien_prix = source.get('prix')
        ancienne_ville = source.get('ville')
        ancienne_adresse = source.get('adresse_exacte')
        ancien_avocat = source.get('avocat')
        
        # Extraire avec la nouvelle logique
        nouveau_prix = extract_prix_flexible(article_text)
        nouvelle_ville = extract_ville_flexible(article_text)
        nouvelle_adresse = extract_adresse_flexible(article_text)
        nouvel_avocat = extract_avocat_flexible(article_text)
        
        # Comparer et mettre à jour si nécessaire
        if nouveau_prix and nouveau_prix != ancien_prix:
            source['prix'] = nouveau_prix
            stats['prix_corriges'] += 1
        
        if nouvelle_ville and nouvelle_ville != ancienne_ville:
            source['ville'] = nouvelle_ville
            stats['ville_corrigee'] += 1
        
        if nouvelle_adresse and not ancienne_adresse:
            source['adresse_exacte'] = nouvelle_adresse
            stats['adresse_ajoutee'] += 1
        
        if nouvel_avocat and nouvel_avocat != ancien_avocat:
            source['avocat'] = nouvel_avocat
            stats['avocat_corrige'] += 1
        
        # Garder des exemples de corrections
        if len(exemples) < 5 and (nouveau_prix != ancien_prix or nouvelle_ville != ancienne_ville or nouvelle_adresse != ancienne_adresse):
            exemples.append({
                'id': article.get('_id', 'N/A'),
                'prix': f"{ancien_prix} → {nouveau_prix}" if nouveau_prix != ancien_prix else ancien_prix,
                'ville': f"{ancienne_ville} → {nouvelle_ville}" if nouvelle_ville != ancienne_ville else ancienne_ville,
                'adresse': nouvelle_adresse if nouvelle_adresse != ancienne_adresse else ancienne_adresse
            })
        
        # Afficher le progrès
        if (i + 1) % 100 == 0:
            print(f"  Traité {i + 1}/{len(articles)} articles...")
    
    # Afficher les statistiques
    print("\n" + "="*60)
    print("📊 STATISTIQUES D'AMÉLIORATION")
    print("="*60)
    print(f"📄 Total des articles: {len(articles)}")
    print(f"💰 Prix corrigés: {stats['prix_corriges']}")
    print(f"🏙️ Villes corrigées: {stats['ville_corrigee']}")
    print(f"🏠 Adresses ajoutées: {stats['adresse_ajoutee']}")
    print(f"⚖️ Avocats corrigés: {stats['avocat_corrige']}")
    
    # Afficher les exemples
    if exemples:
        print("\n📝 EXEMPLES DE CORRECTIONS:")
        for i, exemple in enumerate(exemples, 1):
            print(f"  {i}. ID: {exemple['id']}")
            print(f"     Prix: {exemple['prix']}")
            print(f"     Ville: {exemple['ville']}")
            print(f"     Adresse: {exemple['adresse']}")
    
    # Sauvegarder
    output_filename = 'articles_immobiliers_final_corriges.json'
    try:
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(articles, f, ensure_ascii=False, indent=2)
        print(f"\n✅ Fichier corrigé sauvegardé: {output_filename}")
    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde: {e}")
    
    print("="*60)
    print("✅ Amélioration terminée!")

if __name__ == "__main__":
    process_articles_improved()
