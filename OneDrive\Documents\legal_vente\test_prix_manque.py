#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour comprendre pourquoi le prix est manqué
"""

import re
from typing import Optional

def extract_prix_strict_original(text: str) -> Optional[str]:
    """Version stricte originale (qui manque le prix)"""
    if not text:
        return None
    
    prix_patterns_stricts = [
        r'(?:بثمن افتتاحي قدره|الثمن الافتتاحي|بالأمن الإفتتاحي|ثمن البيع|سعر البيع)\s*[:\s]*([0-9.,\s]+)',
        r'(?:بثمن افتتاحي قدره|الثمن الافتتاحي|بالأمن الإفتتاحي)\s*[^0-9]*\(([0-9.,\s]+)\)',
        r'(?:mise à prix|prix de vente|prix d\'adjudication|montant de la vente)\s*[:\s]*([0-9.,\s]+)',
        r'(?:vendu pour|adjugé pour|cédé pour)\s*[:\s]*([0-9.,\s]+)',
        r'([0-9.,\s]+)\s*(?:dinars?|دينار)\s*(?:tunisiens?)?',
        r'\(([0-9.,\s]+)\s*(?:د|دينار|dinars?)\)',
        r'(?:مائة|ألف|آلاف|مليون)[^(]*(?:دينار)[^(]*\(([0-9.,\s]+)\)',
    ]
    
    for pattern in prix_patterns_stricts:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            prix = match if isinstance(match, str) else match
            prix_clean = re.sub(r'[^\d.,]', '', prix)
            if prix_clean and len(prix_clean) >= 3:
                return prix_clean
    
    return None

def extract_prix_ameliore(text: str) -> Optional[str]:
    """Version améliorée qui devrait capturer le prix manqué"""
    if not text:
        return None
    
    # D'abord, exclure les contextes qui ne sont PAS des prix
    exclusions_contexte = [
        'وصل عدد', 'تسجيل عدد', 'بطاقة التعريف الوطنية عدد',
        'الهاتف', 'الفاكس', 'المعرف الجبائي', 'phone', 'tel', 'fax'
    ]
    
    # Patterns améliorés pour les prix
    prix_patterns_ameliores = [
        # Pattern principal manqué : "الثمن الإفتتاحي : 192.850,864د"
        r'(?:الثمن الإفتتاحي|الثمن الافتتاحي)\s*[:\s]*([0-9.,\s]+)د',
        
        # Autres patterns stricts
        r'(?:بثمن افتتاحي قدره|الثمن الافتتاحي|بالأمن الإفتتاحي|ثمن البيع|سعر البيع)\s*[:\s]*([0-9.,\s]+)',
        r'(?:بثمن افتتاحي قدره|الثمن الافتتاحي|بالأمن الإفتتاحي)\s*[^0-9]*\(([0-9.,\s]+)\)',
        
        # Prix avec contexte français
        r'(?:mise à prix|prix de vente|prix d\'adjudication|montant de la vente)\s*[:\s]*([0-9.,\s]+)',
        r'(?:vendu pour|adjugé pour|cédé pour)\s*[:\s]*([0-9.,\s]+)',
        
        # Prix avec dinars
        r'([0-9.,\s]+)\s*(?:dinars?|دينار|د)\s*(?:tunisiens?)?',
        r'\(([0-9.,\s]+)\s*(?:د|دينار|dinars?)\)',
        
        # Prix avec mots arabes
        r'(?:مائة|ألف|آلاف|مليون)[^(]*(?:دينار)[^(]*\(([0-9.,\s]+)\)',
    ]
    
    for pattern in prix_patterns_ameliores:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            prix = match if isinstance(match, str) else match
            prix_clean = re.sub(r'[^\d.,]', '', prix)
            
            if prix_clean and len(prix_clean) >= 3:
                # Éviter les numéros de téléphone, ID, etc.
                if not re.match(r'^[0-9]{8,10}$', prix_clean):
                    # Vérifier que ce n'est pas dans un contexte d'exclusion
                    if not is_in_exclusion_context(text, prix, exclusions_contexte):
                        return prix_clean
    
    return None

def is_in_exclusion_context(text: str, prix: str, exclusions: list) -> bool:
    """Vérifie si le prix est dans un contexte d'exclusion"""
    prix_escaped = re.escape(prix)
    
    for exclusion in exclusions:
        pattern = f'{exclusion}[^0-9]*{prix_escaped}'
        if re.search(pattern, text, re.IGNORECASE):
            return True
    
    return False

def main():
    # Texte problématique
    exemple_text = """الأستاذ فتحى المولدي المحامي لدى التعقيب 18 نهج العراق - الطابق الأول - لافيات - الهاتف : 71844017 والفاكس : 71844027 بيع عقار بالمزاد العلني (بعد التخفيض بـ 40 بالمائة ) **الدائن العاقل : عزيزة بنت شبيل نصري** المدين المعقول فيه : عبد العزيز الجموسي المحامي القائم بالتتبع : الأستاذ فتحى المولدي المحامي لدى التعقيب 18 نهج العراق تونس . العقار موضوع البيع (نصفه ) : الرسم العقاري عـ 51277 ـدد تونس الكائن بـ 15 نهج الأنوار أريانة العليا . الثمن الإفتتاحي : 192.850,864د (قيمة نصف العقار بعد التخفيض بـ 40 بالمائة دون المصاريف. - تاريخ البتة : يوم 10 جويلية 2023 على الساعة التاسعة صباحا وما يليها أمام المحكمة الإبتدائية بأريانة . - زيارة العقار : يكون كل يوم أحد من الساعة 10 صباحا إلى الساعة الثانية مساء بعد الإعلام المسبق. IP223/5235"""
    
    print("🔍 ANALYSE DU PRIX MANQUÉ")
    print("="*60)
    print("Texte contient: 'الثمن الإفتتاحي : 192.850,864د'")
    print("="*60)
    
    # Tester l'ancienne version
    prix_original = extract_prix_strict_original(exemple_text)
    print(f"❌ Version stricte originale: {prix_original}")
    
    # Tester la version améliorée
    prix_ameliore = extract_prix_ameliore(exemple_text)
    print(f"✅ Version améliorée: {prix_ameliore}")
    
    print(f"\n🎯 Prix attendu: 192.850,864")
    
    # Test du pattern spécifique
    pattern_test = r'(?:الثمن الإفتتاحي|الثمن الافتتاحي)\s*[:\s]*([0-9.,\s]+)د'
    match = re.search(pattern_test, exemple_text)
    if match:
        print(f"🔧 Pattern spécifique trouvé: {match.group(0)}")
        print(f"🔧 Prix extrait: {match.group(1)}")
    
    # Vérifier les exclusions
    print(f"\n🔍 Vérification exclusions:")
    print(f"- Dans contexte téléphone: {is_in_exclusion_context(exemple_text, '192.850,864', ['الهاتف'])}")
    print(f"- Dans contexte fax: {is_in_exclusion_context(exemple_text, '192.850,864', ['الفاكس'])}")

if __name__ == "__main__":
    main()
